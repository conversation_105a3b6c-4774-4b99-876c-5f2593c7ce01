# AWS Image Builder Component: Domain Join Preparation
# This component prepares Windows Server for domain joining

name: DomainJoinPreparation
description: Prepare Windows Server for Active Directory domain joining
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: InstallDomainJoinPrerequisites
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Installing domain join prerequisites..."
              
              try {
                  # Install RSAT tools for domain management
                  Write-Host "Installing Remote Server Administration Tools..."
                  
                  $features = @(
                      "RSAT-AD-PowerShell",
                      "RSAT-ADDS-Tools",
                      "RSAT-DNS-Server"
                  )
                  
                  foreach ($feature in $features) {
                      Write-Host "Checking feature: $feature"
                      $featureState = Get-WindowsFeature -Name $feature -ErrorAction SilentlyContinue
                      
                      if ($featureState) {
                          if ($featureState.InstallState -ne "Installed") {
                              Write-Host "Installing feature: $feature"
                              $result = Install-WindowsFeature -Name $feature -IncludeManagementTools
                              if ($result.Success) {
                                  Write-Host "✓ Successfully installed: $feature"
                              } else {
                                  Write-Warning "Failed to install: $feature"
                              }
                          } else {
                              Write-Host "✓ Already installed: $feature"
                          }
                      } else {
                          Write-Warning "Feature not available: $feature"
                      }
                  }
                  
                  Write-Host "RSAT tools installation completed"
                  
              } catch {
                  Write-Error "Failed to install RSAT tools: $($_.Exception.Message)"
                  exit 1
              }

      - name: ConfigureWindowsTimeService
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Configuring Windows Time Service for domain synchronization..."
              
              try {
                  # Configure time service for domain environment
                  Write-Host "Setting up time synchronization..."
                  
                  # Set time service to manual peer list initially
                  w32tm /config /manualpeerlist:"time.windows.com,0x1" /syncfromflags:manual /reliable:yes /update
                  
                  # Restart time service
                  Restart-Service w32time -Force
                  
                  # Force time sync
                  w32tm /resync /force
                  
                  Write-Host "✓ Windows Time Service configured successfully"
                  
                  # Display current time configuration
                  Write-Host "Current time configuration:"
                  w32tm /query /configuration
                  
              } catch {
                  Write-Warning "Time service configuration had issues: $($_.Exception.Message)"
                  # Don't fail the build for time service issues
              }

      - name: ConfigureNetworkSettings
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Configuring network settings for domain environment..."
              
              try {
                  # Enable NetBIOS over TCP/IP for domain compatibility
                  Write-Host "Configuring NetBIOS settings..."
                  
                  # Get all network adapters
                  $adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
                  
                  foreach ($adapter in $adapters) {
                      Write-Host "Configuring adapter: $($adapter.Name)"
                      
                      # Enable NetBIOS over TCP/IP
                      $regPath = "HKLM:\SYSTEM\CurrentControlSet\Services\NetBT\Parameters\Interfaces"
                      $adapterGuid = $adapter.InterfaceGuid
                      $interfacePath = "$regPath\Tcpip_$adapterGuid"
                      
                      if (Test-Path $interfacePath) {
                          # 0 = Use NetBIOS setting from DHCP server
                          # 1 = Enable NetBIOS over TCP/IP
                          # 2 = Disable NetBIOS over TCP/IP
                          Set-ItemProperty -Path $interfacePath -Name "NetbiosOptions" -Value 1 -ErrorAction SilentlyContinue
                          Write-Host "✓ NetBIOS enabled for adapter: $($adapter.Name)"
                      }
                  }
                  
                  # Configure DNS client settings
                  Write-Host "Configuring DNS client settings..."
                  Set-DnsClientGlobalSetting -SuffixSearchList @() -ErrorAction SilentlyContinue
                  
                  Write-Host "✓ Network settings configured for domain environment"
                  
              } catch {
                  Write-Warning "Network configuration had issues: $($_.Exception.Message)"
                  # Don't fail the build for network configuration issues
              }

      - name: CreateDomainJoinScript
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Creating domain join utility script..."
              
              try {
                  $scriptPath = "C:\Windows\Scripts"
                  if (!(Test-Path $scriptPath)) {
                      New-Item -ItemType Directory -Path $scriptPath -Force | Out-Null
                  }
                  
                  $domainJoinScript = @'
# Domain Join Utility Script
# This script can be used to join the computer to a domain after deployment

param(
    [Parameter(Mandatory=$true)]
    [string]$DomainName,
    
    [Parameter(Mandatory=$true)]
    [string]$Username,
    
    [Parameter(Mandatory=$true)]
    [string]$Password,
    
    [Parameter(Mandatory=$false)]
    [string]$OUPath,
    
    [Parameter(Mandatory=$false)]
    [switch]$Restart = $true
)

Write-Host "Domain Join Utility"
Write-Host "==================="
Write-Host "Domain: $DomainName"
Write-Host "User: $Username"
if ($OUPath) { Write-Host "OU: $OUPath" }

try {
    # Test domain connectivity
    Write-Host "Testing domain connectivity..."
    $domainTest = Test-NetConnection -ComputerName $DomainName -Port 389 -InformationLevel Quiet
    if (!$domainTest) {
        throw "Cannot connect to domain $DomainName on port 389 (LDAP)"
    }
    Write-Host "✓ Domain connectivity verified"
    
    # Create credential object
    $securePassword = ConvertTo-SecureString $Password -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential($Username, $securePassword)
    
    # Join domain
    Write-Host "Joining domain..."
    if ($OUPath) {
        Add-Computer -DomainName $DomainName -Credential $credential -OUPath $OUPath -Force
    } else {
        Add-Computer -DomainName $DomainName -Credential $credential -Force
    }
    
    Write-Host "✓ Successfully joined domain: $DomainName"
    
    if ($Restart) {
        Write-Host "Restarting computer in 10 seconds..."
        Start-Sleep -Seconds 10
        Restart-Computer -Force
    } else {
        Write-Host "Domain join completed. Restart required to complete the process."
    }
    
} catch {
    Write-Error "Domain join failed: $($_.Exception.Message)"
    exit 1
}
'@
                  
                  $scriptFile = "$scriptPath\Join-Domain.ps1"
                  $domainJoinScript | Out-File -FilePath $scriptFile -Encoding UTF8
                  
                  Write-Host "✓ Domain join script created: $scriptFile"
                  
                  # Create a batch file wrapper for easier execution
                  $batchContent = @"
@echo off
echo Domain Join Utility
echo ===================
echo.
echo This script will join the computer to an Active Directory domain.
echo.
echo Usage: Join-Domain.ps1 -DomainName "domain.com" -Username "domain\user" -Password "password"
echo Optional: -OUPath "OU=Servers,DC=domain,DC=com"
echo.
echo Example:
echo PowerShell.exe -ExecutionPolicy Bypass -File "C:\Windows\Scripts\Join-Domain.ps1" -DomainName "contoso.com" -Username "contoso\administrator" -Password "P@ssw0rd123"
echo.
pause
"@
                  
                  $batchFile = "$scriptPath\Join-Domain.bat"
                  $batchContent | Out-File -FilePath $batchFile -Encoding ASCII
                  
                  Write-Host "✓ Domain join batch file created: $batchFile"
                  
              } catch {
                  Write-Error "Failed to create domain join script: $($_.Exception.Message)"
                  exit 1
              }

      - name: ConfigureRegistrySettings
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Configuring registry settings for domain environment..."
              
              try {
                  # Create registry path for domain join information
                  $regPath = "HKLM:\SOFTWARE\Company\DomainJoin"
                  if (!(Test-Path $regPath)) {
                      New-Item -Path $regPath -Force | Out-Null
                      Write-Host "✓ Created registry path: $regPath"
                  }
                  
                  # Set preparation timestamp
                  Set-ItemProperty -Path $regPath -Name "PreparedDate" -Value (Get-Date).ToString()
                  Set-ItemProperty -Path $regPath -Name "PreparedBy" -Value "AWS Image Builder"
                  Set-ItemProperty -Path $regPath -Name "Status" -Value "Prepared"
                  
                  # Configure domain join related registry settings
                  Write-Host "Setting domain-friendly registry configurations..."
                  
                  # Disable automatic logon (security best practice for domain environments)
                  $winLogonPath = "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Winlogon"
                  Set-ItemProperty -Path $winLogonPath -Name "AutoAdminLogon" -Value "0" -ErrorAction SilentlyContinue
                  
                  # Configure LSA settings for domain authentication
                  $lsaPath = "HKLM:\SYSTEM\CurrentControlSet\Control\Lsa"
                  
                  # Enable LM compatibility level for domain environments
                  Set-ItemProperty -Path $lsaPath -Name "LmCompatibilityLevel" -Value 3 -ErrorAction SilentlyContinue
                  
                  # Configure authentication package order
                  $authPackages = @("msv1_0", "kerberos", "negotiate")
                  Set-ItemProperty -Path $lsaPath -Name "Authentication Packages" -Value $authPackages -ErrorAction SilentlyContinue
                  
                  Write-Host "✓ Registry settings configured for domain environment"
                  
              } catch {
                  Write-Warning "Registry configuration had issues: $($_.Exception.Message)"
                  # Don't fail the build for registry issues
              }

      - name: ConfigureFirewallForDomain
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Configuring Windows Firewall for domain environment..."
              
              try {
                  # Enable firewall rules commonly needed for domain operations
                  Write-Host "Enabling domain-related firewall rules..."
                  
                  $domainRules = @(
                      "Core Networking - DNS (UDP-Out)",
                      "Core Networking - DNS (UDP-In)",
                      "Active Directory Domain Services",
                      "Active Directory Domain Services (LDAP-UDP-In)",
                      "Active Directory Domain Services (LDAP-TCP-In)",
                      "Active Directory Domain Services (LDAP-SSL-TCP-In)",
                      "Kerberos Key Distribution Center",
                      "Windows Time",
                      "File and Printer Sharing (Echo Request - ICMPv4-In)",
                      "File and Printer Sharing (Echo Request - ICMPv6-In)"
                  )
                  
                  foreach ($rule in $domainRules) {
                      try {
                          Enable-NetFirewallRule -DisplayName $rule -ErrorAction SilentlyContinue
                          Write-Host "✓ Enabled firewall rule: $rule"
                      } catch {
                          Write-Host "Rule not found or already enabled: $rule"
                      }
                  }
                  
                  # Create custom rules for domain communication
                  Write-Host "Creating custom domain communication rules..."
                  
                  # LDAP
                  New-NetFirewallRule -DisplayName "Domain LDAP (TCP-389)" -Direction Outbound -Protocol TCP -LocalPort 389 -Action Allow -ErrorAction SilentlyContinue
                  New-NetFirewallRule -DisplayName "Domain LDAP SSL (TCP-636)" -Direction Outbound -Protocol TCP -LocalPort 636 -Action Allow -ErrorAction SilentlyContinue
                  
                  # Kerberos
                  New-NetFirewallRule -DisplayName "Domain Kerberos (TCP-88)" -Direction Outbound -Protocol TCP -LocalPort 88 -Action Allow -ErrorAction SilentlyContinue
                  New-NetFirewallRule -DisplayName "Domain Kerberos (UDP-88)" -Direction Outbound -Protocol UDP -LocalPort 88 -Action Allow -ErrorAction SilentlyContinue
                  
                  # DNS
                  New-NetFirewallRule -DisplayName "Domain DNS (UDP-53)" -Direction Outbound -Protocol UDP -LocalPort 53 -Action Allow -ErrorAction SilentlyContinue
                  
                  Write-Host "✓ Windows Firewall configured for domain environment"
                  
              } catch {
                  Write-Warning "Firewall configuration had issues: $($_.Exception.Message)"
                  # Don't fail the build for firewall issues
              }

  - name: validate
    steps:
      - name: ValidateDomainJoinPreparation
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Validating domain join preparation..."
              
              $validationErrors = @()
              
              # Check RSAT tools installation
              $rsatFeatures = @("RSAT-AD-PowerShell", "RSAT-ADDS-Tools")
              foreach ($feature in $rsatFeatures) {
                  $featureState = Get-WindowsFeature -Name $feature -ErrorAction SilentlyContinue
                  if (!$featureState -or $featureState.InstallState -ne "Installed") {
                      $validationErrors += "RSAT feature not installed: $feature"
                  } else {
                      Write-Host "✓ RSAT feature installed: $feature"
                  }
              }
              
              # Check domain join script
              $scriptPath = "C:\Windows\Scripts\Join-Domain.ps1"
              if (!(Test-Path $scriptPath)) {
                  $validationErrors += "Domain join script not found: $scriptPath"
              } else {
                  Write-Host "✓ Domain join script found: $scriptPath"
              }
              
              # Check registry configuration
              $regPath = "HKLM:\SOFTWARE\Company\DomainJoin"
              if (!(Test-Path $regPath)) {
                  $validationErrors += "Domain join registry path not found: $regPath"
              } else {
                  $status = Get-ItemProperty -Path $regPath -Name "Status" -ErrorAction SilentlyContinue
                  if ($status -and $status.Status -eq "Prepared") {
                      Write-Host "✓ Domain join registry configuration verified"
                  } else {
                      $validationErrors += "Domain join registry status not set correctly"
                  }
              }
              
              # Check time service
              $timeService = Get-Service -Name w32time -ErrorAction SilentlyContinue
              if (!$timeService -or $timeService.Status -ne "Running") {
                  $validationErrors += "Windows Time Service is not running"
              } else {
                  Write-Host "✓ Windows Time Service is running"
              }
              
              if ($validationErrors.Count -eq 0) {
                  Write-Host "✓ VALIDATION SUCCESS: System is prepared for domain joining"
                  Write-Host ""
                  Write-Host "To join domain after deployment, use:"
                  Write-Host "C:\Windows\Scripts\Join-Domain.ps1 -DomainName 'domain.com' -Username 'domain\user' -Password 'password'"
                  exit 0
              } else {
                  Write-Error "VALIDATION FAILED:"
                  foreach ($error in $validationErrors) {
                      Write-Error "  - $error"
                  }
                  exit 1
              }
