# AWS Image Builder Examples

This folder contains AWS Image Builder configurations for creating custom Windows Server 2022 AMIs with:
- .NET Framework 4.8
- BGInfo application
- Domain joining capabilities

## Folder Structure

```
ImageBuilder/
├── components/           # Reusable Image Builder components
│   ├── install-dotnet48.yml
│   ├── install-bginfo.yml
│   └── domain-join.yml
├── recipes/             # Image recipes combining components
│   └── windows-server-2022-custom.yml
├── infrastructure/      # Infrastructure configurations
│   └── build-infrastructure.yml
├── distribution/        # Distribution settings
│   └── distribution-config.yml
├── pipelines/          # Complete pipeline definitions
│   └── windows-server-pipeline.yml
├── scripts/            # PowerShell scripts used by components
│   ├── install-bginfo.ps1
│   └── domain-join.ps1
├── deployment/         # Deployment scripts and guides
│   ├── deploy.ps1
│   └── cleanup.ps1
└── README.md           # This file
```

## Component Details

### 1. .NET Framework 4.8 Installation (`install-dotnet48.yml`)
- **Purpose**: Installs Microsoft .NET Framework 4.8 on Windows Server 2022
- **Features**:
  - Checks for existing .NET installations
  - Downloads official Microsoft installer
  - Performs silent installation
  - Validates installation success
  - Handles reboot requirements
- **Validation**: Verifies .NET 4.8 installation via registry check

### 2. BGInfo Installation (`install-bginfo.yml`)
- **Purpose**: Installs and configures Sysinternals BGInfo for system information display
- **Features**:
  - Downloads BGInfo from Microsoft Sysinternals
  - Creates custom configuration showing AWS EC2 instance details
  - Sets up automatic startup via registry and scheduled task
  - Configures EULA acceptance
  - Displays comprehensive system information on desktop
- **Information Displayed**:
  - Computer name and AWS instance details
  - Network configuration
  - Domain information
  - System specifications
  - Boot and logon times
  - Storage information

### 3. Domain Join Preparation (`domain-join.yml`)
- **Purpose**: Prepares Windows Server for Active Directory domain joining
- **Features**:
  - Installs RSAT (Remote Server Administration Tools)
  - Configures Windows Time Service for domain sync
  - Sets up network settings for domain compatibility
  - Creates domain join utility script
  - Configures firewall rules for domain communication
  - Sets registry configurations for domain environment
- **Post-Deployment**: Provides script for easy domain joining after instance launch

## Prerequisites

### AWS Requirements
- AWS CLI configured with appropriate permissions
- VPC with private subnet and NAT Gateway for internet access
- Security group allowing outbound HTTPS, HTTP, DNS, and NTP
- S3 bucket for build logs (optional but recommended)
- SNS topic for build notifications (optional)

### IAM Permissions Required
- EC2ImageBuilderInstanceProfile with permissions for:
  - EC2 instance management
  - S3 access for logs and artifacts
  - SSM Parameter Store access (for domain credentials)
  - CloudWatch Logs access

### Domain Join Requirements (Optional)
- Active Directory domain accessible from AWS
- Domain credentials stored in AWS Systems Manager Parameter Store:
  - `/aws/imagebuilder/domain/username`
  - `/aws/imagebuilder/domain/password` (SecureString)

## Quick Start

1. **Update Configuration Files**:
   - Replace placeholder values in `infrastructure/build-infrastructure.yml`
   - Update account IDs and regions in `distribution/distribution-config.yml`
   - Verify AMI ID in `recipes/windows-server-2022-custom.yml`

2. **Deploy Components**:
   ```powershell
   # Create components
   aws imagebuilder create-component --cli-input-yaml file://components/install-dotnet48.yml
   aws imagebuilder create-component --cli-input-yaml file://components/install-bginfo.yml
   aws imagebuilder create-component --cli-input-yaml file://components/domain-join.yml
   ```

3. **Create Recipe**:
   ```powershell
   aws imagebuilder create-image-recipe --cli-input-yaml file://recipes/windows-server-2022-custom.yml
   ```

4. **Setup Infrastructure**:
   ```powershell
   aws imagebuilder create-infrastructure-configuration --cli-input-yaml file://infrastructure/build-infrastructure.yml
   ```

5. **Configure Distribution**:
   ```powershell
   aws imagebuilder create-distribution-configuration --cli-input-yaml file://distribution/distribution-config.yml
   ```

6. **Create Pipeline**:
   ```powershell
   aws imagebuilder create-image-pipeline --cli-input-yaml file://pipelines/windows-server-pipeline.yml
   ```

7. **Start Build**:
   ```powershell
   aws imagebuilder start-image-pipeline-execution --image-pipeline-arn "your-pipeline-arn"
   ```

## Detailed Deployment Steps

### Step 1: Prepare Your Environment

1. **Update AMI ID**: Find the latest Windows Server 2022 AMI for your region:
   ```powershell
   aws ec2 describe-images --owners amazon --filters "Name=name,Values=Windows_Server-2022-English-Full-Base-*" --query 'Images[*].[ImageId,Name,CreationDate]' --output table
   ```

2. **Create Required IAM Role**:
   ```powershell
   # Create instance profile for Image Builder
   aws iam create-role --role-name EC2ImageBuilderInstanceRole --assume-role-policy-document '{
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Principal": {
           "Service": "ec2.amazonaws.com"
         },
         "Action": "sts:AssumeRole"
       }
     ]
   }'

   # Attach required policies
   aws iam attach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn arn:aws:iam::aws:policy/EC2InstanceProfileForImageBuilder
   aws iam attach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn arn:aws:iam::aws:policy/SSMInstanceCore

   # Create instance profile
   aws iam create-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile
   aws iam add-role-to-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile --role-name EC2ImageBuilderInstanceRole
   ```

3. **Create S3 Bucket for Logs** (Optional):
   ```powershell
   aws s3 mb s3://your-imagebuilder-logs-bucket-unique-name
   ```

### Step 2: Configure Domain Join (Optional)

If you plan to join instances to a domain, store credentials in Parameter Store:

```powershell
# Store domain username
aws ssm put-parameter --name "/aws/imagebuilder/domain/username" --value "DOMAIN\administrator" --type "String"

# Store domain password (encrypted)
aws ssm put-parameter --name "/aws/imagebuilder/domain/password" --value "YourDomainPassword" --type "SecureString"
```

### Step 3: Deploy Image Builder Resources

Use the provided YAML files to create all resources. Make sure to update the placeholder values first:

1. **Update `infrastructure/build-infrastructure.yml`**:
   - Replace `sg-0123456789abcdef0` with your security group ID
   - Replace `subnet-0123456789abcdef0` with your subnet ID
   - Replace `your-key-pair-name` with your EC2 key pair
   - Replace `your-imagebuilder-logs-bucket` with your S3 bucket name

2. **Update `distribution/distribution-config.yml`**:
   - Replace account IDs with your target AWS accounts
   - Add or remove regions as needed

3. **Deploy in Order**:
   ```powershell
   # 1. Create components
   aws imagebuilder create-component --cli-input-yaml file://components/install-dotnet48.yml
   aws imagebuilder create-component --cli-input-yaml file://components/install-bginfo.yml
   aws imagebuilder create-component --cli-input-yaml file://components/domain-join.yml

   # 2. Create infrastructure configuration
   aws imagebuilder create-infrastructure-configuration --cli-input-yaml file://infrastructure/build-infrastructure.yml

   # 3. Create distribution configuration
   aws imagebuilder create-distribution-configuration --cli-input-yaml file://distribution/distribution-config.yml

   # 4. Create image recipe
   aws imagebuilder create-image-recipe --cli-input-yaml file://recipes/windows-server-2022-custom.yml

   # 5. Create pipeline
   aws imagebuilder create-image-pipeline --cli-input-yaml file://pipelines/windows-server-pipeline.yml
   ```

### Step 4: Monitor Build Process

1. **Start Pipeline Execution**:
   ```powershell
   aws imagebuilder start-image-pipeline-execution --image-pipeline-arn "arn:aws:imagebuilder:region:account:image-pipeline/windowsserver2022custompipeline"
   ```

2. **Monitor Progress**:
   ```powershell
   # List pipeline executions
   aws imagebuilder list-image-pipeline-executions --image-pipeline-arn "your-pipeline-arn"

   # Get execution details
   aws imagebuilder get-image --image-build-version-arn "your-image-build-arn"
   ```

3. **Check Logs**:
   - Build logs are available in CloudWatch Logs
   - S3 bucket (if configured) contains detailed build artifacts
   - EC2 console shows the temporary build instance during execution

## Build Process Timeline

The complete build process typically takes 2-4 hours and includes:

1. **Infrastructure Setup** (5-10 minutes)
   - Launch build instance
   - Apply security groups and networking
   - Install Image Builder agent

2. **Component Execution** (60-90 minutes)
   - Install .NET Framework 4.8 (20-30 minutes)
   - Install and configure BGInfo (10-15 minutes)
   - Prepare domain join capabilities (15-20 minutes)
   - Apply Windows updates (30-45 minutes)

3. **Testing Phase** (30-60 minutes)
   - Validate component installations
   - Run built-in Windows tests
   - Verify system functionality

4. **Image Creation** (30-45 minutes)
   - Create AMI snapshot
   - Apply tags and metadata
   - Distribute to target regions/accounts

5. **Cleanup** (5-10 minutes)
   - Terminate build instance
   - Clean up temporary resources

## Troubleshooting

### Common Issues

1. **Build Fails During .NET Installation**:
   - Check internet connectivity from build subnet
   - Verify security group allows outbound HTTPS (443)
   - Review CloudWatch logs for specific error messages

2. **BGInfo Not Displaying**:
   - Verify BGInfo downloaded successfully
   - Check if antivirus is blocking execution
   - Ensure registry entries were created correctly

3. **Domain Join Preparation Fails**:
   - Verify RSAT features are available on Windows Server 2022
   - Check if Windows features installation succeeded
   - Review firewall configuration

4. **Build Instance Access Issues**:
   - Verify IAM role has required permissions
   - Check VPC/subnet configuration
   - Ensure NAT Gateway provides internet access

### Debugging Steps

1. **Enable Detailed Logging**:
   ```powershell
   # Update infrastructure config to keep instance on failure
   # Set terminateInstanceOnFailure: false
   ```

2. **Access Build Instance**:
   - Use Session Manager or RDP to connect to failed build instance
   - Review logs in `C:\ProgramData\Amazon\ImageBuilder\Logs\`
   - Check Windows Event Logs

3. **Component Testing**:
   - Test individual PowerShell scripts manually
   - Verify download URLs are accessible
   - Check registry entries and file installations

## Post-Build Usage

### Launching Instances from Custom AMI

```powershell
# Launch instance using custom AMI
aws ec2 run-instances --image-id ami-your-custom-ami-id --instance-type t3.medium --key-name your-key-pair --security-group-ids sg-your-sg-id --subnet-id subnet-your-subnet-id
```

### Domain Joining After Launch

1. **Using the Provided Script**:
   ```powershell
   # On the launched instance, run:
   C:\Windows\Scripts\Join-Domain.ps1 -DomainName "contoso.com" -Username "contoso\administrator" -Password "YourPassword"
   ```

2. **Using PowerShell Directly**:
   ```powershell
   # Create credential and join domain
   $credential = Get-Credential
   Add-Computer -DomainName "contoso.com" -Credential $credential -Restart
   ```

### Verifying Installation

After launching an instance from your custom AMI:

1. **Check .NET Framework**:
   ```powershell
   Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release
   ```

2. **Verify BGInfo**:
   - Check desktop background for system information
   - Verify startup entries: `Get-ItemProperty "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name BGInfo`

3. **Confirm Domain Join Readiness**:
   ```powershell
   # Check RSAT installation
   Get-WindowsFeature -Name RSAT-AD-PowerShell

   # Verify domain join script
   Test-Path "C:\Windows\Scripts\Join-Domain.ps1"
   ```

## Maintenance and Updates

### Updating Components

1. **Create New Component Version**:
   ```powershell
   # Update version in component YAML file
   aws imagebuilder create-component --cli-input-yaml file://components/install-dotnet48.yml
   ```

2. **Update Recipe**:
   ```powershell
   # Reference new component version in recipe
   aws imagebuilder create-image-recipe --cli-input-yaml file://recipes/windows-server-2022-custom.yml
   ```

3. **Rebuild Pipeline**:
   ```powershell
   aws imagebuilder start-image-pipeline-execution --image-pipeline-arn "your-pipeline-arn"
   ```

### Scheduled Builds

The pipeline is configured to run weekly on Sundays at 2 AM UTC. This ensures:
- Latest Windows updates are included
- Security patches are applied
- Base AMI updates are incorporated

### Cost Optimization

- Build instances are automatically terminated after completion
- Use appropriate instance types (m5.large is usually sufficient)
- Consider using Spot instances for cost savings (not recommended for production)
- Clean up old AMIs periodically to reduce storage costs

## Security Considerations

1. **Network Security**:
   - Use private subnets with NAT Gateway
   - Restrict security group rules to minimum required
   - Enable VPC Flow Logs for monitoring

2. **Access Control**:
   - Use IAM roles instead of access keys
   - Apply least privilege principle
   - Enable CloudTrail for API auditing

3. **Image Security**:
   - Regularly update base AMIs
   - Apply latest security patches
   - Consider using AWS Inspector for vulnerability assessment

4. **Domain Credentials**:
   - Store in SSM Parameter Store with encryption
   - Use dedicated service account with minimal privileges
   - Rotate credentials regularly

## Support and Resources

- **AWS Image Builder Documentation**: https://docs.aws.amazon.com/imagebuilder/
- **Windows Server 2022 Documentation**: https://docs.microsoft.com/en-us/windows-server/
- **Sysinternals BGInfo**: https://docs.microsoft.com/en-us/sysinternals/downloads/bginfo
- **.NET Framework Downloads**: https://dotnet.microsoft.com/download/dotnet-framework
