# AWS Image Builder Base AMI Update Script
# This script automatically updates the base AMI and triggers a new build

param(
    [Parameter(Mandatory=$false)]
    [string]$Region = "us-east-1",
    
    [Parameter(Mandatory=$false)]
    [string]$RecipeName = "WindowsServer2022Custom",
    
    [Parameter(Mandatory=$false)]
    [string]$PipelineName = "WindowsServer2022CustomPipeline",
    
    [Parameter(Mandatory=$false)]
    [switch]$StartBuild = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$UseAMIName = $true,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false
)

Write-Host "AWS Image Builder Base AMI Update Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set AWS region
$env:AWS_DEFAULT_REGION = $Region
Write-Host "Using AWS Region: $Region" -ForegroundColor Yellow

# Function to check if AWS CLI is available and configured
function Test-AWSConfiguration {
    try {
        $identity = aws sts get-caller-identity --output json | ConvertFrom-Json
        Write-Host "✓ AWS CLI configured for account: $($identity.Account)" -ForegroundColor Green
        return $true
    } catch {
        Write-Error "AWS CLI not configured or not available. Please configure AWS CLI first."
        return $false
    }
}

# Function to get latest Windows Server 2022 AMI
function Get-LatestWindowsServer2022AMI {
    Write-Host "Finding latest Windows Server 2022 AMI..." -ForegroundColor Yellow
    
    try {
        $amiInfo = aws ec2 describe-images --owners amazon --filters "Name=name,Values=Windows_Server-2022-English-Full-Base-*" --query 'Images | sort_by(@, &CreationDate) | [-1].[ImageId,Name,CreationDate]' --output json | ConvertFrom-Json
        
        if ($amiInfo -and $amiInfo.Count -eq 3) {
            Write-Host "✓ Found latest AMI:" -ForegroundColor Green
            Write-Host "  ID: $($amiInfo[0])" -ForegroundColor Cyan
            Write-Host "  Name: $($amiInfo[1])" -ForegroundColor Cyan
            Write-Host "  Created: $($amiInfo[2])" -ForegroundColor Cyan
            return @{
                ImageId = $amiInfo[0]
                Name = $amiInfo[1]
                CreationDate = $amiInfo[2]
            }
        } else {
            throw "Could not find Windows Server 2022 AMI"
        }
    } catch {
        Write-Error "Failed to find Windows Server 2022 AMI: $($_.Exception.Message)"
        return $null
    }
}

# Function to get current recipe AMI
function Get-CurrentRecipeAMI {
    param([string]$RecipeName)
    
    Write-Host "Getting current recipe AMI..." -ForegroundColor Yellow
    
    try {
        # Get the latest version of the recipe
        $recipeArn = aws imagebuilder list-image-recipes --query "imageRecipeSummaryList[?name=='$RecipeName'].arn | [0]" --output text
        
        if ($recipeArn -and $recipeArn -ne "None") {
            $recipeDetails = aws imagebuilder get-image-recipe --image-recipe-arn $recipeArn --output json | ConvertFrom-Json
            $currentParentImage = $recipeDetails.imageRecipe.parentImage
            
            Write-Host "✓ Current recipe parent image: $currentParentImage" -ForegroundColor Green
            return $currentParentImage
        } else {
            Write-Warning "Recipe not found: $RecipeName"
            return $null
        }
    } catch {
        Write-Error "Failed to get current recipe AMI: $($_.Exception.Message)"
        return $null
    }
}

# Function to check if AMI update is needed
function Test-AMIUpdateNeeded {
    param(
        [object]$LatestAMI,
        [string]$CurrentAMI
    )
    
    Write-Host "Checking if AMI update is needed..." -ForegroundColor Yellow
    
    # If using AMI name pattern, always consider update needed for scheduled builds
    if ($CurrentAMI -like "Windows_Server-*") {
        Write-Host "✓ Using AMI name pattern - will use latest automatically" -ForegroundColor Green
        return $false  # No manual update needed
    }
    
    # If using specific AMI ID, compare with latest
    if ($CurrentAMI -eq $LatestAMI.ImageId) {
        Write-Host "✓ Recipe is already using the latest AMI" -ForegroundColor Green
        return $false
    } else {
        Write-Host "⚠ Recipe AMI update needed:" -ForegroundColor Yellow
        Write-Host "  Current: $CurrentAMI" -ForegroundColor Red
        Write-Host "  Latest:  $($LatestAMI.ImageId)" -ForegroundColor Green
        return $true
    }
}

# Function to create new recipe version
function New-RecipeVersion {
    param(
        [string]$RecipeName,
        [object]$NewAMI,
        [bool]$UseNamePattern
    )
    
    Write-Host "Creating new recipe version..." -ForegroundColor Yellow
    
    try {
        # Get current recipe
        $recipeArn = aws imagebuilder list-image-recipes --query "imageRecipeSummaryList[?name=='$RecipeName'].arn | [0]" --output text
        $recipeDetails = aws imagebuilder get-image-recipe --image-recipe-arn $recipeArn --output json | ConvertFrom-Json
        
        # Increment version
        $currentVersion = $recipeDetails.imageRecipe.version
        $versionParts = $currentVersion.Split('.')
        $newMinorVersion = [int]$versionParts[2] + 1
        $newVersion = "$($versionParts[0]).$($versionParts[1]).$newMinorVersion"
        
        # Update parent image
        if ($UseNamePattern) {
            $newParentImage = "Windows_Server-2022-English-Full-Base"
        } else {
            $newParentImage = $NewAMI.ImageId
        }
        
        # Create new recipe configuration
        $newRecipeConfig = @{
            name = $recipeDetails.imageRecipe.name
            description = $recipeDetails.imageRecipe.description + " - Updated $(Get-Date -Format 'yyyy-MM-dd')"
            semanticVersion = $newVersion
            parentImage = $newParentImage
            components = $recipeDetails.imageRecipe.components
            workingDirectory = $recipeDetails.imageRecipe.workingDirectory
            tags = $recipeDetails.imageRecipe.tags
        }
        
        # Convert to JSON and create new recipe
        $configJson = $newRecipeConfig | ConvertTo-Json -Depth 10
        $tempFile = [System.IO.Path]::GetTempFileName()
        $configJson | Out-File -FilePath $tempFile -Encoding UTF8
        
        Write-Host "Creating recipe version $newVersion with parent image: $newParentImage" -ForegroundColor Cyan
        $result = aws imagebuilder create-image-recipe --cli-input-json "file://$tempFile"
        
        # Cleanup temp file
        Remove-Item $tempFile -Force
        
        if ($LASTEXITCODE -eq 0) {
            $newRecipeArn = ($result | ConvertFrom-Json).imageRecipeArn
            Write-Host "✓ New recipe version created: $newRecipeArn" -ForegroundColor Green
            return $newRecipeArn
        } else {
            throw "Failed to create new recipe version"
        }
        
    } catch {
        Write-Error "Failed to create new recipe version: $($_.Exception.Message)"
        return $null
    }
}

# Function to update pipeline with new recipe
function Update-PipelineRecipe {
    param(
        [string]$PipelineName,
        [string]$NewRecipeArn
    )
    
    Write-Host "Updating pipeline with new recipe..." -ForegroundColor Yellow
    
    try {
        # Get current pipeline
        $pipelineArn = aws imagebuilder list-image-pipelines --query "imagePipelineList[?name=='$PipelineName'].arn | [0]" --output text
        $pipelineDetails = aws imagebuilder get-image-pipeline --image-pipeline-arn $pipelineArn --output json | ConvertFrom-Json
        
        # Update pipeline configuration
        $updateConfig = @{
            imagePipelineArn = $pipelineArn
            imageRecipeArn = $NewRecipeArn
            infrastructureConfigurationArn = $pipelineDetails.imagePipeline.infrastructureConfigurationArn
            distributionConfigurationArn = $pipelineDetails.imagePipeline.distributionConfigurationArn
            imageTestsConfiguration = $pipelineDetails.imagePipeline.imageTestsConfiguration
            enhancedImageMetadataEnabled = $pipelineDetails.imagePipeline.enhancedImageMetadataEnabled
            schedule = $pipelineDetails.imagePipeline.schedule
            status = $pipelineDetails.imagePipeline.status
        }
        
        # Convert to JSON and update pipeline
        $configJson = $updateConfig | ConvertTo-Json -Depth 10
        $tempFile = [System.IO.Path]::GetTempFileName()
        $configJson | Out-File -FilePath $tempFile -Encoding UTF8
        
        Write-Host "Updating pipeline: $PipelineName" -ForegroundColor Cyan
        aws imagebuilder update-image-pipeline --cli-input-json "file://$tempFile"
        
        # Cleanup temp file
        Remove-Item $tempFile -Force
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Pipeline updated successfully" -ForegroundColor Green
            return $true
        } else {
            throw "Failed to update pipeline"
        }
        
    } catch {
        Write-Error "Failed to update pipeline: $($_.Exception.Message)"
        return $false
    }
}

# Function to start pipeline execution
function Start-PipelineExecution {
    param([string]$PipelineName)
    
    Write-Host "Starting pipeline execution..." -ForegroundColor Yellow
    
    try {
        $pipelineArn = aws imagebuilder list-image-pipelines --query "imagePipelineList[?name=='$PipelineName'].arn | [0]" --output text
        
        if ($pipelineArn -and $pipelineArn -ne "None") {
            aws imagebuilder start-image-pipeline-execution --image-pipeline-arn $pipelineArn | Out-Null
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Pipeline execution started successfully" -ForegroundColor Green
                Write-Host "Monitor progress with:" -ForegroundColor Yellow
                Write-Host "aws imagebuilder list-image-pipeline-executions --image-pipeline-arn $pipelineArn" -ForegroundColor Cyan
                return $true
            } else {
                throw "Failed to start pipeline execution"
            }
        } else {
            throw "Pipeline not found: $PipelineName"
        }
    } catch {
        Write-Error "Failed to start pipeline execution: $($_.Exception.Message)"
        return $false
    }
}

# Main execution
try {
    # Validate prerequisites
    if (!(Test-AWSConfiguration)) {
        exit 1
    }
    
    # Get latest Windows Server 2022 AMI
    $latestAMI = Get-LatestWindowsServer2022AMI
    if (!$latestAMI) {
        exit 1
    }
    
    # Get current recipe AMI
    $currentAMI = Get-CurrentRecipeAMI -RecipeName $RecipeName
    if (!$currentAMI) {
        Write-Warning "Could not determine current recipe AMI. Proceeding with update..."
    }
    
    # Check if update is needed
    $updateNeeded = $true
    if ($currentAMI) {
        $updateNeeded = Test-AMIUpdateNeeded -LatestAMI $latestAMI -CurrentAMI $currentAMI
    }
    
    if ($updateNeeded -or $Force) {
        if ($Force) {
            Write-Host "Forcing update as requested..." -ForegroundColor Yellow
        }
        
        # Create new recipe version
        $newRecipeArn = New-RecipeVersion -RecipeName $RecipeName -NewAMI $latestAMI -UseNamePattern $UseAMIName
        if (!$newRecipeArn) {
            exit 1
        }
        
        # Update pipeline
        if (!(Update-PipelineRecipe -PipelineName $PipelineName -NewRecipeArn $newRecipeArn)) {
            exit 1
        }
        
        # Start build if requested
        if ($StartBuild) {
            if (!(Start-PipelineExecution -PipelineName $PipelineName)) {
                exit 1
            }
        }
        
        Write-Host "`n✓ Base AMI update completed successfully!" -ForegroundColor Green
        Write-Host "New recipe ARN: $newRecipeArn" -ForegroundColor Cyan
        
    } else {
        Write-Host "`n✓ No update needed - recipe is current" -ForegroundColor Green
        
        if ($StartBuild) {
            Write-Host "Starting build with current recipe..." -ForegroundColor Yellow
            Start-PipelineExecution -PipelineName $PipelineName
        }
    }
    
    if (!$StartBuild -and ($updateNeeded -or $Force)) {
        Write-Host "`nTo start a build with the updated recipe, run:" -ForegroundColor Yellow
        Write-Host ".\update-base-ami.ps1 -StartBuild" -ForegroundColor Cyan
    }
    
} catch {
    Write-Error "Base AMI update failed: $($_.Exception.Message)"
    exit 1
}
