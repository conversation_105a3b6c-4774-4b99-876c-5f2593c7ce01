# AWS Image Builder Distribution Configuration
# Defines how and where the custom Windows Server 2022 AMI will be distributed

name: WindowsServer2022Distribution
description: Distribution configuration for custom Windows Server 2022 AMI

# Distribution settings
distributions:
  # Primary region distribution
  - region: us-east-1
    amiDistributionConfiguration:
      name: "CustomWindowsServer2022-{{ imagebuilder:buildDate }}"
      description: "Custom Windows Server 2022 with .NET 4.8, BGInfo, and domain join preparation - Built on {{ imagebuilder:buildDate }}"
      
      # AMI tags
      amiTags:
        Name: "CustomWindowsServer2022-{{ imagebuilder:buildDate }}"
        OS: "Windows Server 2022"
        BaseAMI: "{{ imagebuilder:parentImage }}"
        BuildDate: "{{ imagebuilder:buildDate }}"
        Components: ".NET 4.8, BGInfo, Domain Join Ready"
        Environment: "Production"
        Version: "1.0.0"
        CreatedBy: "AWS Image Builder"
        Purpose: "Base Windows Server Image"
        Compliance: "CIS Benchmark Ready"
        
      # Target accounts for AMI sharing (optional)
      targetAccountIds:
        - "************"  # Replace with target AWS account IDs
        - "************"  # Add more account IDs as needed
      
      # Launch permissions
      launchPermission:
        userIds:
          - "************"  # Replace with AWS account IDs that can launch instances
          - "************"
        # userGroups:
        #   - "all"  # Uncomment to make AMI public (not recommended for production)

  # Secondary region distribution (optional)
  - region: us-west-2
    amiDistributionConfiguration:
      name: "CustomWindowsServer2022-{{ imagebuilder:buildDate }}-west"
      description: "Custom Windows Server 2022 with .NET 4.8, BGInfo, and domain join preparation - Built on {{ imagebuilder:buildDate }} (West Coast)"
      
      amiTags:
        Name: "CustomWindowsServer2022-{{ imagebuilder:buildDate }}-west"
        OS: "Windows Server 2022"
        BaseAMI: "{{ imagebuilder:parentImage }}"
        BuildDate: "{{ imagebuilder:buildDate }}"
        Components: ".NET 4.8, BGInfo, Domain Join Ready"
        Environment: "Production"
        Version: "1.0.0"
        CreatedBy: "AWS Image Builder"
        Purpose: "Base Windows Server Image"
        Region: "us-west-2"
        
      targetAccountIds:
        - "************"
        - "************"
      
      launchPermission:
        userIds:
          - "************"
          - "************"

  # Additional region (EU) - uncomment if needed
  # - region: eu-west-1
  #   amiDistributionConfiguration:
  #     name: "CustomWindowsServer2022-{{ imagebuilder:buildDate }}-eu"
  #     description: "Custom Windows Server 2022 with .NET 4.8, BGInfo, and domain join preparation - Built on {{ imagebuilder:buildDate }} (Europe)"
  #     
  #     amiTags:
  #       Name: "CustomWindowsServer2022-{{ imagebuilder:buildDate }}-eu"
  #       OS: "Windows Server 2022"
  #       BaseAMI: "{{ imagebuilder:parentImage }}"
  #       BuildDate: "{{ imagebuilder:buildDate }}"
  #       Components: ".NET 4.8, BGInfo, Domain Join Ready"
  #       Environment: "Production"
  #       Version: "1.0.0"
  #       CreatedBy: "AWS Image Builder"
  #       Purpose: "Base Windows Server Image"
  #       Region: "eu-west-1"
  #       
  #     targetAccountIds:
  #       - "************"
  #       - "************"
  #     
  #     launchPermission:
  #       userIds:
  #         - "************"
  #         - "************"

# License configuration for Windows
licenseConfigurationArns:
  - "arn:aws:license-manager:us-east-1:************:license-configuration:lic-0123456789abcdef0"  # Replace with your Windows license configuration ARN if using License Manager
