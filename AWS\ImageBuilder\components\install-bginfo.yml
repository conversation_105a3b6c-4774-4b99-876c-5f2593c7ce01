# AWS Image Builder Component: Install BGInfo
# This component downloads, installs, and configures BGInfo to display system information

name: InstallBGInfo
description: Install and configure BGInfo to display system information on desktop
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: CreateTempDirectory
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Creating temporary directory for BGInfo installation..."
              $tempDir = "C:\temp"
              if (!(Test-Path $tempDir)) {
                  New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
                  Write-Host "Created directory: $tempDir"
              } else {
                  Write-Host "Directory already exists: $tempDir"
              }

      - name: DownloadBGInfo
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $downloadUrl = "https://download.sysinternals.com/files/BGInfo.zip"
              $zipPath = "C:\temp\BGInfo.zip"
              
              Write-Host "Downloading BGInfo from Sysinternals..."
              Write-Host "URL: $downloadUrl"
              
              try {
                  # Download with progress
                  $webClient = New-Object System.Net.WebClient
                  $webClient.DownloadFile($downloadUrl, $zipPath)
                  
                  # Verify download
                  if (Test-Path $zipPath) {
                      $fileSize = (Get-Item $zipPath).Length
                      Write-Host "Download completed successfully. File size: $fileSize bytes"
                  } else {
                      throw "Download failed - file not found"
                  }
              } catch {
                  Write-Error "Failed to download BGInfo: $($_.Exception.Message)"
                  exit 1
              }

      - name: InstallBGInfo
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $zipPath = "C:\temp\BGInfo.zip"
              $installPath = "C:\Program Files\BGInfo"
              
              Write-Host "Installing BGInfo to: $installPath"
              
              try {
                  # Create installation directory
                  if (!(Test-Path $installPath)) {
                      New-Item -ItemType Directory -Path $installPath -Force | Out-Null
                      Write-Host "Created installation directory"
                  }
                  
                  # Extract BGInfo using .NET
                  Add-Type -AssemblyName System.IO.Compression.FileSystem
                  [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $installPath)
                  
                  # Verify extraction
                  $bginfoExe = "$installPath\Bginfo.exe"
                  if (Test-Path $bginfoExe) {
                      $version = (Get-Item $bginfoExe).VersionInfo.FileVersion
                      Write-Host "BGInfo extracted successfully. Version: $version"
                  } else {
                      throw "BGInfo.exe not found after extraction"
                  }
                  
              } catch {
                  Write-Error "Failed to extract BGInfo: $($_.Exception.Message)"
                  exit 1
              }

      - name: ConfigureBGInfo
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $installPath = "C:\Program Files\BGInfo"
              $configPath = "$installPath\bginfo.bgi"
              $bginfoExe = "$installPath\Bginfo.exe"
              
              Write-Host "Configuring BGInfo..."
              
              # Create custom BGInfo configuration with comprehensive system info
              $bgiConfig = @"
[BGInfo]
RTF={\rtf1\ansi\deff0{\fonttbl{\f0\fnil\fcharset0 Arial;}}{\colortbl ;\red255\green255\blue255;\red0\green0\blue0;\red255\green0\blue0;}
\cf2\fs16\b AWS EC2 Instance Information\b0\fs14
\line Computer Name: \cf3\b<Computer Name>\cf2\b0
\line Instance ID: \cf3\b<EC2 Instance ID>\cf2\b0
\line Instance Type: \cf3\b<EC2 Instance Type>\cf2\b0
\line Availability Zone: \cf3\b<EC2 Availability Zone>\cf2\b0
\line
\line \fs16\b Network Information\b0\fs14
\line IP Address: \cf3\b<IP Address>\cf2\b0
\line Subnet Mask: \cf3\b<Subnet Mask>\cf2\b0
\line Default Gateway: \cf3\b<Default Gateway>\cf2\b0
\line DNS Server: \cf3\b<DNS Server>\cf2\b0
\line
\line \fs16\b Domain Information\b0\fs14
\line Domain: \cf3\b<Domain>\cf2\b0
\line Logon Domain: \cf3\b<Logon Domain>\cf2\b0
\line Logon Server: \cf3\b<Logon Server>\cf2\b0
\line
\line \fs16\b System Information\b0\fs14
\line OS Version: \cf3\b<OS Version>\cf2\b0
\line System Type: \cf3\b<System Type>\cf2\b0
\line Processor: \cf3\b<Processor>\cf2\b0
\line Memory: \cf3\b<Memory>\cf2\b0
\line
\line \fs16\b Timing Information\b0\fs14
\line Boot Time: \cf3\b<Boot Time>\cf2\b0
\line Logon Time: \cf3\b<Logon Time>\cf2\b0
\line
\line \fs16\b Storage Information\b0\fs14
\line Free Space C:: \cf3\b<Free Space C:>\cf2\b0
\line Free Space D:: \cf3\b<Free Space D:>\cf2\b0
}
Position=0
TextWidth=400
TextHeight=0
Transparency=200
Timeout=0
"@
              
              try {
                  # Save configuration
                  $bgiConfig | Out-File -FilePath $configPath -Encoding ASCII
                  Write-Host "BGInfo configuration saved to: $configPath"
                  
                  # Accept EULA in registry for current user and default user
                  Write-Host "Accepting BGInfo EULA..."
                  
                  # Current user
                  $registryPath = "HKCU:\Software\Sysinternals\BGInfo"
                  if (!(Test-Path $registryPath)) {
                      New-Item -Path $registryPath -Force | Out-Null
                  }
                  Set-ItemProperty -Path $registryPath -Name "EulaAccepted" -Value 1
                  
                  # Default user profile
                  reg load HKU\DefaultUser "C:\Users\<USER>\NTUSER.DAT" 2>$null
                  $defaultUserPath = "HKU:\DefaultUser\Software\Sysinternals\BGInfo"
                  if (!(Test-Path $defaultUserPath)) {
                      New-Item -Path $defaultUserPath -Force | Out-Null
                  }
                  Set-ItemProperty -Path $defaultUserPath -Name "EulaAccepted" -Value 1
                  reg unload HKU\DefaultUser 2>$null
                  
                  Write-Host "EULA accepted for current and default users"
                  
              } catch {
                  Write-Error "Failed to configure BGInfo: $($_.Exception.Message)"
                  exit 1
              }

      - name: SetupStartupTask
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $installPath = "C:\Program Files\BGInfo"
              $configPath = "$installPath\bginfo.bgi"
              $bginfoExe = "$installPath\Bginfo.exe"
              
              Write-Host "Setting up BGInfo to run at startup..."
              
              try {
                  # Create startup registry entry for all users
                  $startupRegPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
                  $startupCommand = "`"$bginfoExe`" `"$configPath`" /timer:0 /nolicprompt"
                  Set-ItemProperty -Path $startupRegPath -Name "BGInfo" -Value $startupCommand
                  
                  Write-Host "BGInfo startup entry created in registry"
                  Write-Host "Command: $startupCommand"
                  
                  # Also create a scheduled task as backup method
                  $taskName = "BGInfo Startup"
                  $taskDescription = "Display system information on desktop using BGInfo"
                  
                  # Remove existing task if it exists
                  try {
                      Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue
                  } catch {}
                  
                  # Create new scheduled task
                  $action = New-ScheduledTaskAction -Execute $bginfoExe -Argument "`"$configPath`" /timer:0 /nolicprompt"
                  $trigger = New-ScheduledTaskTrigger -AtLogOn
                  $principal = New-ScheduledTaskPrincipal -GroupId "Users" -RunLevel Limited
                  $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
                  
                  Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description $taskDescription
                  
                  Write-Host "BGInfo scheduled task created successfully"
                  
              } catch {
                  Write-Error "Failed to setup BGInfo startup: $($_.Exception.Message)"
                  exit 1
              }

      - name: TestBGInfo
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              $installPath = "C:\Program Files\BGInfo"
              $configPath = "$installPath\bginfo.bgi"
              $bginfoExe = "$installPath\Bginfo.exe"
              
              Write-Host "Testing BGInfo installation..."
              
              try {
                  # Test run BGInfo (it will update the desktop background)
                  Write-Host "Running BGInfo test..."
                  $process = Start-Process -FilePath $bginfoExe -ArgumentList "`"$configPath`"", "/timer:0", "/nolicprompt" -Wait -PassThru
                  
                  if ($process.ExitCode -eq 0) {
                      Write-Host "BGInfo test run completed successfully"
                  } else {
                      Write-Warning "BGInfo test run completed with exit code: $($process.ExitCode)"
                  }
                  
                  # Verify files exist
                  if (Test-Path $bginfoExe) {
                      Write-Host "✓ BGInfo executable found: $bginfoExe"
                  }
                  if (Test-Path $configPath) {
                      Write-Host "✓ BGInfo configuration found: $configPath"
                  }
                  
                  # Verify registry entries
                  $startupEntry = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name "BGInfo" -ErrorAction SilentlyContinue
                  if ($startupEntry) {
                      Write-Host "✓ Startup registry entry verified"
                  }
                  
                  # Verify scheduled task
                  $task = Get-ScheduledTask -TaskName "BGInfo Startup" -ErrorAction SilentlyContinue
                  if ($task) {
                      Write-Host "✓ Scheduled task verified"
                  }
                  
                  Write-Host "BGInfo installation and configuration completed successfully!"
                  
              } catch {
                  Write-Error "BGInfo test failed: $($_.Exception.Message)"
                  exit 1
              }

      - name: Cleanup
        action: ExecutePowerShell
        onFailure: Continue
        inputs:
          commands:
            - |
              Write-Host "Cleaning up installation files..."
              
              $filesToClean = @(
                  "C:\temp\BGInfo.zip"
              )
              
              foreach ($file in $filesToClean) {
                  if (Test-Path $file) {
                      try {
                          Remove-Item $file -Force
                          Write-Host "Removed: $file"
                      } catch {
                          Write-Warning "Could not remove: $file - $($_.Exception.Message)"
                      }
                  }
              }
              
              Write-Host "Cleanup completed"

  - name: validate
    steps:
      - name: ValidateBGInfo
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Validating BGInfo installation..."
              
              $installPath = "C:\Program Files\BGInfo"
              $configPath = "$installPath\bginfo.bgi"
              $bginfoExe = "$installPath\Bginfo.exe"
              
              $validationErrors = @()
              
              # Check if BGInfo executable exists
              if (!(Test-Path $bginfoExe)) {
                  $validationErrors += "BGInfo executable not found: $bginfoExe"
              }
              
              # Check if configuration file exists
              if (!(Test-Path $configPath)) {
                  $validationErrors += "BGInfo configuration not found: $configPath"
              }
              
              # Check startup registry entry
              $startupEntry = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run" -Name "BGInfo" -ErrorAction SilentlyContinue
              if (!$startupEntry) {
                  $validationErrors += "BGInfo startup registry entry not found"
              }
              
              # Check scheduled task
              $task = Get-ScheduledTask -TaskName "BGInfo Startup" -ErrorAction SilentlyContinue
              if (!$task) {
                  $validationErrors += "BGInfo scheduled task not found"
              }
              
              if ($validationErrors.Count -eq 0) {
                  Write-Host "✓ VALIDATION SUCCESS: BGInfo is properly installed and configured"
                  Write-Host "BGInfo will display system information on the desktop at startup"
                  exit 0
              } else {
                  Write-Error "VALIDATION FAILED:"
                  foreach ($error in $validationErrors) {
                      Write-Error "  - $error"
                  }
                  exit 1
              }
