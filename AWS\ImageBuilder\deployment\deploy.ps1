# AWS Image Builder Deployment Script
# This script automates the deployment of the Windows Server 2022 custom image pipeline

param(
    [Parameter(Mandatory=$true)]
    [string]$Region = "us-east-1",
    
    [Parameter(Mandatory=$true)]
    [string]$SecurityGroupId,
    
    [Parameter(Mandatory=$true)]
    [string]$SubnetId,
    
    [Parameter(Mandatory=$false)]
    [string]$KeyPairName,
    
    [Parameter(Mandatory=$false)]
    [string]$S3LogsBucket,
    
    [Parameter(Mandatory=$false)]
    [string]$SnsTopicArn,
    
    [Parameter(Mandatory=$false)]
    [string[]]$TargetAccountIds = @(),
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateIAMRole = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$StartBuild = $false,

    [Parameter(Mandatory=$false)]
    [switch]$UseAMIName,

    [Parameter(Mandatory=$false)]
    [switch]$UpdateExistingRecipe = $false
)

Write-Host "AWS Image Builder Deployment Script" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

# Set AWS region
$env:AWS_DEFAULT_REGION = $Region
Write-Host "Using AWS Region: $Region" -ForegroundColor Yellow

# Function to check if AWS CLI is available and configured
function Test-AWSConfiguration {
    try {
        $identity = aws sts get-caller-identity --output json | ConvertFrom-Json
        Write-Host "✓ AWS CLI configured for account: $($identity.Account)" -ForegroundColor Green
        return $true
    } catch {
        Write-Error "AWS CLI not configured or not available. Please configure AWS CLI first."
        return $false
    }
}

# Function to get latest Windows Server 2022 AMI
function Get-LatestWindowsServer2022AMI {
    Write-Host "Finding latest Windows Server 2022 AMI..." -ForegroundColor Yellow
    
    try {
        $amiInfo = aws ec2 describe-images --owners amazon --filters "Name=name,Values=Windows_Server-2022-English-Full-Base-*" --query 'Images | sort_by(@, &CreationDate) | [-1].[ImageId,Name]' --output json | ConvertFrom-Json
        
        if ($amiInfo -and $amiInfo.Count -eq 2) {
            Write-Host "✓ Found AMI: $($amiInfo[0]) - $($amiInfo[1])" -ForegroundColor Green
            return $amiInfo[0]
        } else {
            throw "Could not find Windows Server 2022 AMI"
        }
    } catch {
        Write-Error "Failed to find Windows Server 2022 AMI: $($_.Exception.Message)"
        return $null
    }
}

# Function to create IAM role for Image Builder
function New-ImageBuilderIAMRole {
    Write-Host "Creating IAM role for Image Builder..." -ForegroundColor Yellow
    
    try {
        # Check if role already exists
        $existingRole = aws iam get-role --role-name EC2ImageBuilderInstanceRole 2>$null
        if ($existingRole) {
            Write-Host "✓ IAM role already exists: EC2ImageBuilderInstanceRole" -ForegroundColor Green
            return $true
        }
        
        # Create trust policy
        $trustPolicy = @{
            Version = "2012-10-17"
            Statement = @(
                @{
                    Effect = "Allow"
                    Principal = @{
                        Service = "ec2.amazonaws.com"
                    }
                    Action = "sts:AssumeRole"
                }
            )
        } | ConvertTo-Json -Depth 10
        
        # Create role
        aws iam create-role --role-name EC2ImageBuilderInstanceRole --assume-role-policy-document $trustPolicy
        
        # Attach policies
        aws iam attach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn "arn:aws:iam::aws:policy/EC2InstanceProfileForImageBuilder"
        aws iam attach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn "arn:aws:iam::aws:policy/SSMInstanceCore"
        
        # Create instance profile
        aws iam create-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile
        aws iam add-role-to-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile --role-name EC2ImageBuilderInstanceRole
        
        Write-Host "✓ IAM role created successfully" -ForegroundColor Green
        return $true
        
    } catch {
        Write-Error "Failed to create IAM role: $($_.Exception.Message)"
        return $false
    }
}

# Function to update configuration files
function Update-ConfigurationFiles {
    param(
        [string]$WindowsAMI,
        [string]$SecurityGroup,
        [string]$Subnet,
        [string]$KeyPair,
        [string]$LogsBucket,
        [string]$SnsArn,
        [string[]]$AccountIds
    )
    
    Write-Host "Updating configuration files..." -ForegroundColor Yellow
    
    try {
        # Update infrastructure configuration
        $infraConfigPath = "infrastructure/build-infrastructure.yml"
        if (Test-Path $infraConfigPath) {
            $infraConfig = Get-Content $infraConfigPath -Raw
            $infraConfig = $infraConfig -replace "sg-0123456789abcdef0", $SecurityGroup
            $infraConfig = $infraConfig -replace "subnet-0123456789abcdef0", $Subnet
            
            if ($KeyPair) {
                $infraConfig = $infraConfig -replace "your-key-pair-name", $KeyPair
            }
            
            if ($LogsBucket) {
                $infraConfig = $infraConfig -replace "your-imagebuilder-logs-bucket", $LogsBucket
            }
            
            if ($SnsArn) {
                $infraConfig = $infraConfig -replace "arn:aws:sns:us-east-1:************:imagebuilder-notifications", $SnsArn
            }
            
            $infraConfig | Set-Content $infraConfigPath
            Write-Host "✓ Updated infrastructure configuration" -ForegroundColor Green
        }
        
        # Update recipe with latest AMI
        $recipeConfigPath = "recipes/windows-server-2022-custom.yml"
        if (Test-Path $recipeConfigPath) {
            $recipeConfig = Get-Content $recipeConfigPath -Raw

            if ($UseAMIName) {
                # Use AMI name pattern for automatic updates
                $recipeConfig = $recipeConfig -replace "parentImage:.*", "parentImage: Windows_Server-2022-English-Full-Base"
                Write-Host "✓ Updated recipe to use AMI name pattern for automatic updates" -ForegroundColor Green
            } else {
                # Use specific AMI ID
                $recipeConfig = $recipeConfig -replace "parentImage:.*", "parentImage: $WindowsAMI"
                Write-Host "✓ Updated recipe configuration with specific AMI: $WindowsAMI" -ForegroundColor Green
            }

            $recipeConfig | Set-Content $recipeConfigPath
        }
        
        # Update distribution configuration
        if ($AccountIds.Count -gt 0) {
            $distConfigPath = "distribution/distribution-config.yml"
            if (Test-Path $distConfigPath) {
                $distConfig = Get-Content $distConfigPath -Raw
                $accountIdList = ($AccountIds | ForEach-Object { "        - `"$_`"" }) -join "`n"
                $distConfig = $distConfig -replace "        - `"************`".*?        - `"************`"", $accountIdList
                $distConfig | Set-Content $distConfigPath
                Write-Host "✓ Updated distribution configuration with target accounts" -ForegroundColor Green
            }
        }
        
        return $true
        
    } catch {
        Write-Error "Failed to update configuration files: $($_.Exception.Message)"
        return $false
    }
}

# Function to deploy Image Builder components
function Deploy-ImageBuilderComponents {
    Write-Host "Deploying Image Builder components..." -ForegroundColor Yellow
    
    $components = @(
        @{ Name = "InstallDotNet48"; Path = "components/install-dotnet48.yml" },
        @{ Name = "InstallBGInfo"; Path = "components/install-bginfo.yml" },
        @{ Name = "DomainJoinPreparation"; Path = "components/domain-join.yml" }
    )
    
    foreach ($component in $components) {
        try {
            Write-Host "Creating component: $($component.Name)..." -ForegroundColor Cyan
            $result = aws imagebuilder create-component --cli-input-yaml "file://$($component.Path)" 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Component created: $($component.Name)" -ForegroundColor Green
            } else {
                if ($result -like "*already exists*") {
                    Write-Host "✓ Component already exists: $($component.Name)" -ForegroundColor Yellow
                } else {
                    throw "Failed to create component: $result"
                }
            }
        } catch {
            Write-Error "Failed to create component $($component.Name): $($_.Exception.Message)"
            return $false
        }
    }
    
    return $true
}

# Function to deploy Image Builder configurations
function Deploy-ImageBuilderConfigurations {
    Write-Host "Deploying Image Builder configurations..." -ForegroundColor Yellow
    
    $configs = @(
        @{ Name = "Infrastructure Configuration"; Path = "infrastructure/build-infrastructure.yml"; Command = "create-infrastructure-configuration" },
        @{ Name = "Distribution Configuration"; Path = "distribution/distribution-config.yml"; Command = "create-distribution-configuration" },
        @{ Name = "Image Recipe"; Path = "recipes/windows-server-2022-custom.yml"; Command = "create-image-recipe" },
        @{ Name = "Image Pipeline"; Path = "pipelines/windows-server-pipeline.yml"; Command = "create-image-pipeline" }
    )
    
    foreach ($config in $configs) {
        try {
            Write-Host "Creating $($config.Name)..." -ForegroundColor Cyan
            $result = aws imagebuilder $($config.Command) --cli-input-yaml "file://$($config.Path)" 2>&1
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ $($config.Name) created successfully" -ForegroundColor Green
            } else {
                if ($result -like "*already exists*") {
                    Write-Host "✓ $($config.Name) already exists" -ForegroundColor Yellow
                } else {
                    throw "Failed to create configuration: $result"
                }
            }
        } catch {
            Write-Error "Failed to create $($config.Name): $($_.Exception.Message)"
            return $false
        }
    }
    
    return $true
}

# Function to start image build
function Start-ImageBuild {
    Write-Host "Starting image build..." -ForegroundColor Yellow
    
    try {
        # Get pipeline ARN
        $pipelineList = aws imagebuilder list-image-pipelines --query 'imagePipelineList[?name==`WindowsServer2022CustomPipeline`].arn' --output text
        
        if ($pipelineList) {
            Write-Host "Starting pipeline execution..." -ForegroundColor Cyan
            aws imagebuilder start-image-pipeline-execution --image-pipeline-arn $pipelineList | Out-Null
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Image build started successfully" -ForegroundColor Green
                Write-Host "Monitor progress in AWS Console or use:" -ForegroundColor Yellow
                Write-Host "aws imagebuilder list-image-pipeline-executions --image-pipeline-arn $pipelineList" -ForegroundColor Cyan
                return $true
            } else {
                throw "Failed to start pipeline execution"
            }
        } else {
            throw "Pipeline not found: WindowsServer2022CustomPipeline"
        }
    } catch {
        Write-Error "Failed to start image build: $($_.Exception.Message)"
        return $false
    }
}

# Main execution
try {
    # Validate prerequisites
    if (!(Test-AWSConfiguration)) {
        exit 1
    }
    
    # Create IAM role if requested
    if ($CreateIAMRole) {
        if (!(New-ImageBuilderIAMRole)) {
            exit 1
        }
        
        Write-Host "Waiting for IAM role propagation..." -ForegroundColor Yellow
        Start-Sleep -Seconds 30
    }
    
    # Get latest Windows Server 2022 AMI
    $latestAMI = Get-LatestWindowsServer2022AMI
    if (!$latestAMI) {
        exit 1
    }
    
    # Update configuration files
    if (!(Update-ConfigurationFiles -WindowsAMI $latestAMI -SecurityGroup $SecurityGroupId -Subnet $SubnetId -KeyPair $KeyPairName -LogsBucket $S3LogsBucket -SnsArn $SnsTopicArn -AccountIds $TargetAccountIds)) {
        exit 1
    }
    
    # Deploy components
    if (!(Deploy-ImageBuilderComponents)) {
        exit 1
    }
    
    # Deploy configurations
    if (!(Deploy-ImageBuilderConfigurations)) {
        exit 1
    }
    
    # Start build if requested
    if ($StartBuild) {
        if (!(Start-ImageBuild)) {
            exit 1
        }
    }
    
    Write-Host "`n✓ Deployment completed successfully!" -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Yellow
    Write-Host "1. Monitor the build process in AWS Console" -ForegroundColor White
    Write-Host "2. Check CloudWatch Logs for detailed build information" -ForegroundColor White
    Write-Host "3. Once complete, launch instances using the new custom AMI" -ForegroundColor White
    
    if (!$StartBuild) {
        Write-Host "`nTo start the build manually, run:" -ForegroundColor Yellow
        Write-Host "aws imagebuilder start-image-pipeline-execution --image-pipeline-arn <pipeline-arn>" -ForegroundColor Cyan
    }
    
} catch {
    Write-Error "Deployment failed: $($_.Exception.Message)"
    exit 1
}
