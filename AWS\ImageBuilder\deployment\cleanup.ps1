# AWS Image Builder Cleanup Script
# This script removes all Image Builder resources created by the deployment

param(
    [Parameter(Mandatory=$false)]
    [string]$Region = "us-east-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$DeleteIAMRole = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$DeleteAMIs = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$Force = $false
)

Write-Host "AWS Image Builder Cleanup Script" -ForegroundColor Red
Write-Host "================================" -ForegroundColor Red

if (!$Force) {
    Write-Host "WARNING: This will delete Image Builder resources!" -ForegroundColor Yellow
    $confirmation = Read-Host "Are you sure you want to continue? (yes/no)"
    if ($confirmation -ne "yes") {
        Write-Host "Cleanup cancelled." -ForegroundColor Yellow
        exit 0
    }
}

# Set AWS region
$env:AWS_DEFAULT_REGION = $Region
Write-Host "Using AWS Region: $Region" -ForegroundColor Yellow

# Function to check if AWS CLI is available and configured
function Test-AWSConfiguration {
    try {
        $identity = aws sts get-caller-identity --output json | ConvertFrom-Json
        Write-Host "✓ AWS CLI configured for account: $($identity.Account)" -ForegroundColor Green
        return $true
    } catch {
        Write-Error "AWS CLI not configured or not available. Please configure AWS CLI first."
        return $false
    }
}

# Function to delete Image Builder pipeline
function Remove-ImageBuilderPipeline {
    Write-Host "Deleting Image Builder pipeline..." -ForegroundColor Yellow
    
    try {
        # Get pipeline ARN
        $pipelineArn = aws imagebuilder list-image-pipelines --query 'imagePipelineList[?name==`WindowsServer2022CustomPipeline`].arn' --output text
        
        if ($pipelineArn) {
            Write-Host "Deleting pipeline: $pipelineArn" -ForegroundColor Cyan
            aws imagebuilder delete-image-pipeline --image-pipeline-arn $pipelineArn
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Pipeline deleted successfully" -ForegroundColor Green
            } else {
                Write-Warning "Failed to delete pipeline"
            }
        } else {
            Write-Host "✓ No pipeline found to delete" -ForegroundColor Yellow
        }
    } catch {
        Write-Warning "Error deleting pipeline: $($_.Exception.Message)"
    }
}

# Function to delete Image Builder recipe
function Remove-ImageBuilderRecipe {
    Write-Host "Deleting Image Builder recipe..." -ForegroundColor Yellow
    
    try {
        # Get recipe ARN
        $recipeArn = aws imagebuilder list-image-recipes --query 'imageRecipeSummaryList[?name==`WindowsServer2022Custom`].arn' --output text
        
        if ($recipeArn) {
            Write-Host "Deleting recipe: $recipeArn" -ForegroundColor Cyan
            aws imagebuilder delete-image-recipe --image-recipe-arn $recipeArn
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Recipe deleted successfully" -ForegroundColor Green
            } else {
                Write-Warning "Failed to delete recipe"
            }
        } else {
            Write-Host "✓ No recipe found to delete" -ForegroundColor Yellow
        }
    } catch {
        Write-Warning "Error deleting recipe: $($_.Exception.Message)"
    }
}

# Function to delete Image Builder configurations
function Remove-ImageBuilderConfigurations {
    Write-Host "Deleting Image Builder configurations..." -ForegroundColor Yellow
    
    # Delete distribution configuration
    try {
        $distConfigArn = aws imagebuilder list-distribution-configurations --query 'distributionConfigurationSummaryList[?name==`WindowsServer2022Distribution`].arn' --output text
        
        if ($distConfigArn) {
            Write-Host "Deleting distribution configuration: $distConfigArn" -ForegroundColor Cyan
            aws imagebuilder delete-distribution-configuration --distribution-configuration-arn $distConfigArn
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Distribution configuration deleted" -ForegroundColor Green
            } else {
                Write-Warning "Failed to delete distribution configuration"
            }
        } else {
            Write-Host "✓ No distribution configuration found" -ForegroundColor Yellow
        }
    } catch {
        Write-Warning "Error deleting distribution configuration: $($_.Exception.Message)"
    }
    
    # Delete infrastructure configuration
    try {
        $infraConfigArn = aws imagebuilder list-infrastructure-configurations --query 'infrastructureConfigurationSummaryList[?name==`WindowsServer2022BuildInfrastructure`].arn' --output text
        
        if ($infraConfigArn) {
            Write-Host "Deleting infrastructure configuration: $infraConfigArn" -ForegroundColor Cyan
            aws imagebuilder delete-infrastructure-configuration --infrastructure-configuration-arn $infraConfigArn
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ Infrastructure configuration deleted" -ForegroundColor Green
            } else {
                Write-Warning "Failed to delete infrastructure configuration"
            }
        } else {
            Write-Host "✓ No infrastructure configuration found" -ForegroundColor Yellow
        }
    } catch {
        Write-Warning "Error deleting infrastructure configuration: $($_.Exception.Message)"
    }
}

# Function to delete Image Builder components
function Remove-ImageBuilderComponents {
    Write-Host "Deleting Image Builder components..." -ForegroundColor Yellow
    
    $componentNames = @("InstallDotNet48", "InstallBGInfo", "DomainJoinPreparation")
    
    foreach ($componentName in $componentNames) {
        try {
            # Get component ARN
            $componentArn = aws imagebuilder list-components --owner Self --query "componentVersionList[?name=='$componentName'].arn" --output text
            
            if ($componentArn) {
                Write-Host "Deleting component: $componentName" -ForegroundColor Cyan
                aws imagebuilder delete-component --component-build-version-arn $componentArn
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✓ Component deleted: $componentName" -ForegroundColor Green
                } else {
                    Write-Warning "Failed to delete component: $componentName"
                }
            } else {
                Write-Host "✓ Component not found: $componentName" -ForegroundColor Yellow
            }
        } catch {
            Write-Warning "Error deleting component $componentName : $($_.Exception.Message)"
        }
    }
}

# Function to delete custom AMIs
function Remove-CustomAMIs {
    Write-Host "Deleting custom AMIs..." -ForegroundColor Yellow
    
    try {
        # Find AMIs created by Image Builder with our naming pattern
        $customAMIs = aws ec2 describe-images --owners self --filters "Name=name,Values=CustomWindowsServer2022-*" --query 'Images[].{ImageId:ImageId,Name:Name}' --output json | ConvertFrom-Json
        
        if ($customAMIs -and $customAMIs.Count -gt 0) {
            foreach ($ami in $customAMIs) {
                Write-Host "Deleting AMI: $($ami.ImageId) - $($ami.Name)" -ForegroundColor Cyan
                
                # Get snapshots associated with the AMI
                $snapshots = aws ec2 describe-images --image-ids $ami.ImageId --query 'Images[].BlockDeviceMappings[].Ebs.SnapshotId' --output text
                
                # Deregister AMI
                aws ec2 deregister-image --image-id $ami.ImageId
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✓ AMI deregistered: $($ami.ImageId)" -ForegroundColor Green
                    
                    # Delete associated snapshots
                    if ($snapshots) {
                        foreach ($snapshot in $snapshots.Split()) {
                            if ($snapshot -and $snapshot.Trim() -ne "") {
                                Write-Host "Deleting snapshot: $snapshot" -ForegroundColor Cyan
                                aws ec2 delete-snapshot --snapshot-id $snapshot.Trim()
                                
                                if ($LASTEXITCODE -eq 0) {
                                    Write-Host "✓ Snapshot deleted: $snapshot" -ForegroundColor Green
                                } else {
                                    Write-Warning "Failed to delete snapshot: $snapshot"
                                }
                            }
                        }
                    }
                } else {
                    Write-Warning "Failed to deregister AMI: $($ami.ImageId)"
                }
            }
        } else {
            Write-Host "✓ No custom AMIs found to delete" -ForegroundColor Yellow
        }
    } catch {
        Write-Warning "Error deleting custom AMIs: $($_.Exception.Message)"
    }
}

# Function to delete IAM role
function Remove-IAMRole {
    Write-Host "Deleting IAM role..." -ForegroundColor Yellow
    
    try {
        # Check if role exists
        $roleExists = aws iam get-role --role-name EC2ImageBuilderInstanceRole 2>$null
        
        if ($roleExists) {
            # Detach policies
            Write-Host "Detaching policies from role..." -ForegroundColor Cyan
            aws iam detach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn "arn:aws:iam::aws:policy/EC2InstanceProfileForImageBuilder"
            aws iam detach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn "arn:aws:iam::aws:policy/SSMInstanceCore"
            
            # Remove role from instance profile
            aws iam remove-role-from-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile --role-name EC2ImageBuilderInstanceRole
            
            # Delete instance profile
            aws iam delete-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile
            
            # Delete role
            aws iam delete-role --role-name EC2ImageBuilderInstanceRole
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ IAM role deleted successfully" -ForegroundColor Green
            } else {
                Write-Warning "Failed to delete IAM role"
            }
        } else {
            Write-Host "✓ IAM role not found" -ForegroundColor Yellow
        }
    } catch {
        Write-Warning "Error deleting IAM role: $($_.Exception.Message)"
    }
}

# Function to cancel running builds
function Stop-RunningBuilds {
    Write-Host "Checking for running builds..." -ForegroundColor Yellow
    
    try {
        # Get running image builds
        $runningBuilds = aws imagebuilder list-images --query 'imageVersionList[?state.status==`BUILDING`].arn' --output text
        
        if ($runningBuilds) {
            foreach ($buildArn in $runningBuilds.Split()) {
                if ($buildArn -and $buildArn.Trim() -ne "") {
                    Write-Host "Cancelling build: $buildArn" -ForegroundColor Cyan
                    aws imagebuilder cancel-image-creation --image-build-version-arn $buildArn.Trim()
                    
                    if ($LASTEXITCODE -eq 0) {
                        Write-Host "✓ Build cancelled: $buildArn" -ForegroundColor Green
                    } else {
                        Write-Warning "Failed to cancel build: $buildArn"
                    }
                }
            }
        } else {
            Write-Host "✓ No running builds found" -ForegroundColor Yellow
        }
    } catch {
        Write-Warning "Error checking running builds: $($_.Exception.Message)"
    }
}

# Main execution
try {
    # Validate prerequisites
    if (!(Test-AWSConfiguration)) {
        exit 1
    }
    
    Write-Host "Starting cleanup process..." -ForegroundColor Yellow
    
    # Cancel any running builds first
    Stop-RunningBuilds
    
    # Delete in reverse order of creation
    Remove-ImageBuilderPipeline
    Remove-ImageBuilderRecipe
    Remove-ImageBuilderConfigurations
    Remove-ImageBuilderComponents
    
    # Delete AMIs if requested
    if ($DeleteAMIs) {
        Remove-CustomAMIs
    }
    
    # Delete IAM role if requested
    if ($DeleteIAMRole) {
        Remove-IAMRole
    }
    
    Write-Host "`n✓ Cleanup completed!" -ForegroundColor Green
    
    if (!$DeleteAMIs) {
        Write-Host "`nNote: Custom AMIs were not deleted. Use -DeleteAMIs to remove them." -ForegroundColor Yellow
    }
    
    if (!$DeleteIAMRole) {
        Write-Host "Note: IAM role was not deleted. Use -DeleteIAMRole to remove it." -ForegroundColor Yellow
    }
    
    Write-Host "`nRemaining resources to clean up manually (if any):" -ForegroundColor Yellow
    Write-Host "- S3 bucket for logs (if created)" -ForegroundColor White
    Write-Host "- SNS topic for notifications (if created)" -ForegroundColor White
    Write-Host "- CloudWatch Log Groups" -ForegroundColor White
    
} catch {
    Write-Error "Cleanup failed: $($_.Exception.Message)"
    exit 1
}
