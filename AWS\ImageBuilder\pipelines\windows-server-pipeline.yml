# AWS Image Builder Pipeline: Complete Windows Server 2022 Custom Image Pipeline
# This pipeline orchestrates the entire image building process

name: WindowsServer2022CustomPipeline
description: Complete pipeline for building custom Windows Server 2022 AMI with .NET 4.8, BGInfo, and domain join preparation

# Pipeline configuration
imageRecipeArn: "arn:aws:imagebuilder:us-east-1:123456789012:image-recipe/windowsserver2022custom/1.0.0"  # Replace with your image recipe ARN
infrastructureConfigurationArn: "arn:aws:imagebuilder:us-east-1:123456789012:infrastructure-configuration/windowsserver2022buildinfrastructure"  # Replace with your infrastructure config ARN
distributionConfigurationArn: "arn:aws:imagebuilder:us-east-1:123456789012:distribution-configuration/windowsserver2022distribution"  # Replace with your distribution config ARN

# Image testing configuration
imageTestsConfiguration:
  imageTestsEnabled: true
  timeoutMinutes: 720  # 12 hours timeout for comprehensive testing

# Schedule configuration (optional)
schedule:
  scheduleExpression: "cron(0 2 ? * SUN *)"  # Run every Sunday at 2 AM UTC
  pipelineExecutionStartCondition: "EXPRESSION_MATCH_AND_DEPENDENCY_UPDATES_AVAILABLE"

# Enhanced logging
enhancedImageMetadataEnabled: true

# Pipeline tags
tags:
  Name: WindowsServer2022CustomPipeline
  Environment: Production
  OS: Windows Server 2022
  Purpose: Custom AMI Creation
  Schedule: Weekly
  Owner: Infrastructure Team
  Components: ".NET 4.8, BGInfo, Domain Join Ready"
