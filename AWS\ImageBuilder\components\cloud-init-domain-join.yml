# AWS Image Builder Component: Cloud-Init Domain Join Setup
# This component prepares the system for cloud-init based domain joining with business-specific configurations

name: CloudInitDomainJoinSetup
description: Setup cloud-init for automatic domain joining with business-specific OU placement
schemaVersion: 1.0
phases:
  - name: build
    steps:
      - name: InstallCloudInit
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Installing and configuring cloud-init for Windows..."
              
              try {
                  # Download and install cloud-init for Windows
                  $cloudInitUrl = "https://github.com/cloudbase/cloudbase-init/releases/download/1.1.4/CloudbaseInitSetup_1_1_4_x64.msi"
                  $installerPath = "C:\temp\CloudbaseInitSetup.msi"
                  
                  # Create temp directory
                  if (!(Test-Path "C:\temp")) {
                      New-Item -ItemType Directory -Path "C:\temp" -Force | Out-Null
                  }
                  
                  Write-Host "Downloading cloud-init installer..."
                  Invoke-WebRequest -Uri $cloudInitUrl -OutFile $installerPath -UseBasicParsing
                  
                  Write-Host "Installing cloud-init..."
                  Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", $installerPath, "/quiet", "/norestart" -Wait
                  
                  if ($LASTEXITCODE -eq 0) {
                      Write-Host "✓ Cloud-init installed successfully"
                  } else {
                      throw "Cloud-init installation failed with exit code: $LASTEXITCODE"
                  }
                  
              } catch {
                  Write-Error "Failed to install cloud-init: $($_.Exception.Message)"
                  exit 1
              }

      - name: CreateDomainJoinScripts
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Creating domain join scripts for cloud-init..."
              
              try {
                  $scriptsPath = "C:\Scripts"
                  if (!(Test-Path $scriptsPath)) {
                      New-Item -ItemType Directory -Path $scriptsPath -Force | Out-Null
                  }
                  
                  # Create the main domain join script
                  $domainJoinScript = @'
# Cloud-Init Domain Join Script
# This script reads business configuration and joins the computer to the appropriate OU

param(
    [Parameter(Mandatory=$false)]
    [string]$ConfigPath = "C:\Scripts\business-config.json",
    
    [Parameter(Mandatory=$false)]
    [string]$ServerRole = "WebServer-2022",  # Default role
    
    [Parameter(Mandatory=$false)]
    [string]$DomainUser,
    
    [Parameter(Mandatory=$false)]
    [string]$DomainPassword
)

$logFile = "C:\Scripts\domain-join.log"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

try {
    Write-Log "Starting cloud-init domain join process"
    
    # Read business configuration
    if (!(Test-Path $ConfigPath)) {
        throw "Business configuration not found: $ConfigPath"
    }
    
    $config = Get-Content $ConfigPath -Raw | ConvertFrom-Json
    Write-Log "Loaded configuration for business: $($config.businessName)"
    
    # Get server role from user data or use default
    if ($env:SERVER_ROLE) {
        $ServerRole = $env:SERVER_ROLE
    }
    
    Write-Log "Server role: $ServerRole"
    
    # Get target OU for this server role
    if ($config.serverOUs.$ServerRole) {
        $targetOU = $config.serverOUs.$ServerRole
        Write-Log "Target OU: $targetOU"
    } else {
        throw "Server role '$ServerRole' not found in configuration"
    }
    
    # Get domain credentials from user data or parameters
    if (!$DomainUser -and $env:DOMAIN_USER) {
        $DomainUser = $env:DOMAIN_USER
    }
    if (!$DomainPassword -and $env:DOMAIN_PASSWORD) {
        $DomainPassword = $env:DOMAIN_PASSWORD
    }
    
    if (!$DomainUser -or !$DomainPassword) {
        throw "Domain credentials not provided"
    }
    
    # Generate computer name with business prefix
    $computerName = $env:COMPUTERNAME
    if ($config.computerNamePrefix) {
        $suffix = $computerName.Substring($computerName.Length - 4)  # Keep last 4 chars
        $newComputerName = "$($config.computerNamePrefix)-$suffix"
        
        Write-Log "Renaming computer from $computerName to $newComputerName"
        Rename-Computer -NewName $newComputerName -Force
        $computerName = $newComputerName
    }
    
    # Test domain connectivity
    Write-Log "Testing connectivity to domain: $($config.domain)"
    $domainTest = Test-NetConnection -ComputerName $config.domain -Port 389 -InformationLevel Quiet
    if (!$domainTest) {
        throw "Cannot connect to domain $($config.domain) on port 389"
    }
    Write-Log "✓ Domain connectivity verified"
    
    # Create credential object
    $securePassword = ConvertTo-SecureString $DomainPassword -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential($DomainUser, $securePassword)
    
    # Join domain with specific OU
    Write-Log "Joining domain $($config.domain) in OU: $targetOU"
    Add-Computer -DomainName $config.domain -Credential $credential -OUPath $targetOU -Force
    
    Write-Log "✓ Successfully joined domain"
    Write-Log "Computer will restart to complete domain join"
    
    # Schedule restart
    shutdown /r /t 60 /c "Restarting to complete domain join"
    
} catch {
    Write-Log "Domain join failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.Exception.StackTrace)" "ERROR"
    exit 1
}
'@
                  
                  $domainJoinScript | Out-File -FilePath "$scriptsPath\cloud-init-domain-join.ps1" -Encoding UTF8
                  Write-Host "✓ Created domain join script"
                  
              } catch {
                  Write-Error "Failed to create domain join scripts: $($_.Exception.Message)"
                  exit 1
              }

      - name: CreateBusinessConfigTemplate
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Creating business configuration template..."
              
              try {
                  $scriptsPath = "C:\Scripts"
                  
                  # Create a template configuration that will be replaced by user data
                  $templateConfig = @{
                      businessName = "TEMPLATE"
                      domain = "example.com"
                      basePath = "OU=TEMPLATE,DC=example,DC=com"
                      serverOUs = @{
                          "WebServer-2022" = "OU=Web Server,OU=Server 2022,DC=example,DC=com"
                          "SQLServer-2022" = "OU=SQL Server,OU=Server 2022,DC=example,DC=com"
                      }
                      computerNamePrefix = "TEMP"
                  }
                  
                  $templateConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath "$scriptsPath\business-config-template.json" -Encoding UTF8
                  Write-Host "✓ Created business configuration template"
                  
              } catch {
                  Write-Error "Failed to create business configuration template: $($_.Exception.Message)"
                  exit 1
              }

      - name: ConfigureCloudInit
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Configuring cloud-init..."
              
              try {
                  $cloudInitConfigPath = "C:\Program Files\Cloudbase Solutions\Cloudbase-Init\conf"
                  
                  # Create cloud-init configuration
                  $cloudInitConfig = @"
[DEFAULT]
username=Administrator
groups=Administrators
inject_user_password=true
config_drive_raw_hhd=true
config_drive_cdrom=true
config_drive_vfat=true
bsdtar_path=C:\Program Files\Cloudbase Solutions\Cloudbase-Init\bin\bsdtar.exe
mtools_path=C:\Program Files\Cloudbase Solutions\Cloudbase-Init\bin\
verbose=true
debug=true
logdir=C:\Program Files\Cloudbase Solutions\Cloudbase-Init\log\
logfile=cloudbase-init.log
default_log_levels=comtypes=INFO,suds=INFO,iso8601=WARN,requests=WARN
logging_serial_port_settings=COM1,115200,N,8
mtu_use_dhcp_config=true
ntp_use_dhcp_config=true
local_scripts_path=C:\Program Files\Cloudbase Solutions\Cloudbase-Init\LocalScripts\

plugins=cloudbaseinit.plugins.common.mtu.MTUPlugin,
        cloudbaseinit.plugins.windows.ntpclient.NTPClientPlugin,
        cloudbaseinit.plugins.common.sethostname.SetHostNamePlugin,
        cloudbaseinit.plugins.windows.createuser.CreateUserPlugin,
        cloudbaseinit.plugins.common.networkconfig.NetworkConfigPlugin,
        cloudbaseinit.plugins.windows.licensing.WindowsLicensingPlugin,
        cloudbaseinit.plugins.common.sshpublickeys.SetUserSSHPublicKeysPlugin,
        cloudbaseinit.plugins.windows.extendvolumes.ExtendVolumesPlugin,
        cloudbaseinit.plugins.common.userdata.UserDataPlugin,
        cloudbaseinit.plugins.common.localscripts.LocalScriptsPlugin

allow_reboot=true
stop_service_on_exit=false
"@
                  
                  $cloudInitConfig | Out-File -FilePath "$cloudInitConfigPath\cloudbase-init.conf" -Encoding UTF8
                  Write-Host "✓ Cloud-init configuration created"
                  
                  # Create local script for domain join
                  $localScriptsPath = "C:\Program Files\Cloudbase Solutions\Cloudbase-Init\LocalScripts"
                  if (!(Test-Path $localScriptsPath)) {
                      New-Item -ItemType Directory -Path $localScriptsPath -Force | Out-Null
                  }
                  
                  $localScript = @'
@echo off
REM Cloud-init local script for domain join
echo Starting domain join process...

REM Check if business config exists in user data
if exist "C:\Scripts\business-config.json" (
    echo Business configuration found, proceeding with domain join...
    PowerShell.exe -ExecutionPolicy Bypass -File "C:\Scripts\cloud-init-domain-join.ps1"
) else (
    echo No business configuration found, skipping domain join
)
'@
                  
                  $localScript | Out-File -FilePath "$localScriptsPath\domain-join.cmd" -Encoding ASCII
                  Write-Host "✓ Local script created"
                  
              } catch {
                  Write-Error "Failed to configure cloud-init: $($_.Exception.Message)"
                  exit 1
              }

  - name: validate
    steps:
      - name: ValidateCloudInitSetup
        action: ExecutePowerShell
        inputs:
          commands:
            - |
              Write-Host "Validating cloud-init domain join setup..."
              
              $validationErrors = @()
              
              # Check cloud-init installation
              $cloudInitService = Get-Service -Name "cloudbase-init" -ErrorAction SilentlyContinue
              if (!$cloudInitService) {
                  $validationErrors += "Cloud-init service not found"
              } else {
                  Write-Host "✓ Cloud-init service found"
              }
              
              # Check scripts
              $requiredFiles = @(
                  "C:\Scripts\cloud-init-domain-join.ps1",
                  "C:\Scripts\business-config-template.json",
                  "C:\Program Files\Cloudbase Solutions\Cloudbase-Init\LocalScripts\domain-join.cmd"
              )
              
              foreach ($file in $requiredFiles) {
                  if (!(Test-Path $file)) {
                      $validationErrors += "Required file not found: $file"
                  } else {
                      Write-Host "✓ Found: $file"
                  }
              }
              
              # Check cloud-init configuration
              $configFile = "C:\Program Files\Cloudbase Solutions\Cloudbase-Init\conf\cloudbase-init.conf"
              if (!(Test-Path $configFile)) {
                  $validationErrors += "Cloud-init configuration not found: $configFile"
              } else {
                  Write-Host "✓ Cloud-init configuration found"
              }
              
              if ($validationErrors.Count -eq 0) {
                  Write-Host "✓ VALIDATION SUCCESS: Cloud-init domain join setup is complete"
                  Write-Host ""
                  Write-Host "To use this AMI:"
                  Write-Host "1. Include business configuration in EC2 user data"
                  Write-Host "2. Set environment variables for domain credentials"
                  Write-Host "3. Instance will automatically join the correct OU"
                  exit 0
              } else {
                  Write-Error "VALIDATION FAILED:"
                  foreach ($error in $validationErrors) {
                      Write-Error "  - $error"
                  }
                  exit 1
              }
