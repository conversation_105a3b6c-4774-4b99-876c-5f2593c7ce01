# AWS Image Builder Recipe: Windows Server 2022 Custom
# This recipe creates a custom Windows Server 2022 AMI with .NET 4.8, BGInfo, and domain join preparation

name: WindowsServer2022Custom
description: Custom Windows Server 2022 with .NET Framework 4.8, BGInfo, and domain join preparation
schemaVersion: 1.0
version: 1.0.0

# Base image - Windows Server 2022 (automatically uses latest)
# Option 1: Use AMI name pattern (automatically gets latest)
parentImage: Windows_Server-2022-English-Full-Base

# Option 2: Use specific AMI ID (manual updates required)
# parentImage: ami-0c02fb55956c7d316

# Build and test components
components:
# AWS managed components for basic Windows configuration
- name: update-windows
  parameters:
  - name: exclude
    value:
    - "KB5005463" # Example: exclude specific updates if needed
  - name: include
    value: []

# Custom component: Install .NET Framework 4.8
- name: InstallDotNet48
  parameters: []

# Custom component: Install and configure BGInfo
- name: InstallBGInfo
  parameters: []

# Custom component: Prepare for domain joining
- name: DomainJoinPreparation
  parameters: []

# AWS managed component for final cleanup and optimization
- name: reboot-windows
  parameters: []

# Working directory for build process
workingDirectory: C:\ImageBuilder

# Additional metadata
tags:
  Environment: Production
  OS: Windows Server 2022
  Version: "1.0.0"
  Components: ".NET 4.8, BGInfo, Domain Join Ready"
  CreatedBy: AWS Image Builder
  Purpose: Base Windows Server Image
