# EC2 User Data Script Template for Business-Specific Domain Join
# This script is executed when an EC2 instance launches from your custom AMI

<powershell>
# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Create log file
$logFile = "C:\Scripts\user-data.log"
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    $logEntry | Out-File -FilePath $logFile -Append -Encoding UTF8
}

Write-Log "Starting user data execution"

try {
    # Create business configuration from template
    # REPLACE THIS SECTION WITH YOUR ACTUAL BUSINESS CONFIG
    $businessConfig = @{
        businessName = "Business1"
        domain = "mud.internal.co.za"
        basePath = "OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
        serverOUs = @{
            "SQLServer-2019" = "OU=SQL Server,OU=Server 2019,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
            "SQLServer-2022" = "OU=SQL Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
            "WebServer-2019" = "OU=Web Server,OU=Server 2019,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
            "WebServer-2022" = "OU=Web Server,OU=Server 2022,OU=Windows Servers,OU=Servers,OU=Business1,OU=Businesses,DC=mud,DC=internal,DC=co,DC=za"
        }
        computerNamePrefix = "BUS1"
    }
    
    # Save business configuration
    $configPath = "C:\Scripts\business-config.json"
    $businessConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath $configPath -Encoding UTF8
    Write-Log "Business configuration saved to: $configPath"
    
    # Set environment variables for domain join
    # REPLACE THESE WITH YOUR ACTUAL CREDENTIALS OR USE PARAMETER STORE
    [Environment]::SetEnvironmentVariable("SERVER_ROLE", "WebServer-2022", "Machine")
    [Environment]::SetEnvironmentVariable("DOMAIN_USER", "mud\administrator", "Machine")
    [Environment]::SetEnvironmentVariable("DOMAIN_PASSWORD", "YourDomainPassword", "Machine")
    
    Write-Log "Environment variables set for domain join"
    
    # Optional: Install additional software based on server role
    $serverRole = "WebServer-2022"  # This could come from EC2 tags or user data parameters
    
    switch ($serverRole) {
        "WebServer-2022" {
            Write-Log "Installing IIS for Web Server role"
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole -All
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-ASPNET45 -All
        }
        "SQLServer-2022" {
            Write-Log "Preparing for SQL Server installation"
            # Add SQL Server specific preparations here
        }
        default {
            Write-Log "No specific role configuration for: $serverRole"
        }
    }
    
    Write-Log "User data execution completed successfully"
    
} catch {
    Write-Log "User data execution failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Stack trace: $($_.Exception.StackTrace)" "ERROR"
}
</powershell>
