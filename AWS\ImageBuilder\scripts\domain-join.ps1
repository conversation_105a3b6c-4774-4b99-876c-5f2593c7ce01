# PowerShell Script: Domain Join Configuration
# This script prepares the system for domain joining and can perform the actual join

param(
    [Parameter(Mandatory=$false)]
    [string]$DomainName,
    
    [Parameter(Mandatory=$false)]
    [string]$DomainUserParameter = "/aws/imagebuilder/domain/username",
    
    [Parameter(Mandatory=$false)]
    [string]$DomainPasswordParameter = "/aws/imagebuilder/domain/password",
    
    [Parameter(Mandatory=$false)]
    [string]$OUPath,
    
    [Parameter(Mandatory=$false)]
    [switch]$PrepareOnly = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$JoinNow = $false
)

Write-Host "Starting domain join configuration..."

function Get-SSMParameter {
    param(
        [string]$ParameterName,
        [switch]$SecureString
    )
    
    try {
        if ($SecureString) {
            $result = aws ssm get-parameter --name $ParameterName --with-decryption --query 'Parameter.Value' --output text
        } else {
            $result = aws ssm get-parameter --name $ParameterName --query 'Parameter.Value' --output text
        }
        
        if ($LASTEXITCODE -eq 0 -and $result -ne "None") {
            return $result
        } else {
            throw "Parameter not found or access denied"
        }
    } catch {
        Write-Warning "Could not retrieve parameter $ParameterName : $($_.Exception.Message)"
        return $null
    }
}

function Test-DomainConnectivity {
    param([string]$Domain)
    
    Write-Host "Testing connectivity to domain: $Domain"
    
    try {
        # Test DNS resolution of domain
        $domainIPs = Resolve-DnsName -Name $Domain -Type A -ErrorAction Stop
        Write-Host "✓ Domain DNS resolution successful"
        
        # Test connectivity to domain controllers
        $dcTest = Test-NetConnection -ComputerName $Domain -Port 389 -InformationLevel Quiet
        if ($dcTest) {
            Write-Host "✓ LDAP connectivity to domain successful"
            return $true
        } else {
            Write-Warning "✗ LDAP connectivity to domain failed"
            return $false
        }
    } catch {
        Write-Warning "✗ Domain connectivity test failed: $($_.Exception.Message)"
        return $false
    }
}

function Install-DomainJoinPrerequisites {
    Write-Host "Installing domain join prerequisites..."
    
    try {
        # Ensure required Windows features are installed
        $features = @(
            "RSAT-AD-PowerShell",
            "RSAT-ADDS-Tools"
        )
        
        foreach ($feature in $features) {
            Write-Host "Checking Windows feature: $feature"
            $featureState = Get-WindowsFeature -Name $feature -ErrorAction SilentlyContinue
            
            if ($featureState -and $featureState.InstallState -ne "Installed") {
                Write-Host "Installing feature: $feature"
                Install-WindowsFeature -Name $feature -IncludeManagementTools
            } elseif ($featureState) {
                Write-Host "✓ Feature already installed: $feature"
            } else {
                Write-Warning "Feature not available: $feature"
            }
        }
        
        # Configure Windows Time Service to sync with domain
        Write-Host "Configuring Windows Time Service..."
        w32tm /config /manualpeerlist:"time.windows.com" /syncfromflags:manual /reliable:yes /update
        Restart-Service w32time -Force
        
        Write-Host "✓ Prerequisites installed successfully"
        return $true
        
    } catch {
        Write-Error "Failed to install prerequisites: $($_.Exception.Message)"
        return $false
    }
}

function Set-DomainJoinConfiguration {
    param(
        [string]$Domain,
        [string]$Username,
        [string]$Password,
        [string]$OU
    )
    
    Write-Host "Configuring domain join settings..."
    
    try {
        # Create domain join configuration script for later use
        $joinScriptPath = "C:\Windows\Temp\domain-join.ps1"
        
        $joinScript = @"
# Auto-generated domain join script
param(
    [switch]`$Execute = `$false
)

if (-not `$Execute) {
    Write-Host "This script will join the computer to domain: $Domain"
    Write-Host "Run with -Execute parameter to perform the join"
    exit 0
}

Write-Host "Joining computer to domain: $Domain"

try {
    # Create credential object
    `$securePassword = ConvertTo-SecureString "$Password" -AsPlainText -Force
    `$credential = New-Object System.Management.Automation.PSCredential("$Username", `$securePassword)
    
    # Join domain
"@

        if ($OU) {
            $joinScript += @"
    Add-Computer -DomainName "$Domain" -Credential `$credential -OUPath "$OU" -Restart -Force
"@
        } else {
            $joinScript += @"
    Add-Computer -DomainName "$Domain" -Credential `$credential -Restart -Force
"@
        }

        $joinScript += @"
    
    Write-Host "Domain join initiated successfully. System will restart."
    
} catch {
    Write-Error "Domain join failed: `$(`$_.Exception.Message)"
    exit 1
}
"@

        # Save the join script
        $joinScript | Out-File -FilePath $joinScriptPath -Encoding UTF8
        Write-Host "✓ Domain join script created: $joinScriptPath"
        
        # Set registry values for domain join information
        $regPath = "HKLM:\SOFTWARE\Company\DomainJoin"
        if (!(Test-Path $regPath)) {
            New-Item -Path $regPath -Force | Out-Null
        }
        
        Set-ItemProperty -Path $regPath -Name "DomainName" -Value $Domain
        Set-ItemProperty -Path $regPath -Name "Username" -Value $Username
        Set-ItemProperty -Path $regPath -Name "Configured" -Value (Get-Date).ToString()
        
        if ($OU) {
            Set-ItemProperty -Path $regPath -Name "OUPath" -Value $OU
        }
        
        Write-Host "✓ Domain join configuration saved to registry"
        return $true
        
    } catch {
        Write-Error "Failed to configure domain join: $($_.Exception.Message)"
        return $false
    }
}

function Join-DomainNow {
    param(
        [string]$Domain,
        [string]$Username,
        [string]$Password,
        [string]$OU
    )
    
    Write-Host "Performing domain join now..."
    
    try {
        # Test domain connectivity first
        if (!(Test-DomainConnectivity -Domain $Domain)) {
            throw "Cannot connect to domain $Domain"
        }
        
        # Create credential object
        $securePassword = ConvertTo-SecureString $Password -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($Username, $securePassword)
        
        # Perform domain join
        Write-Host "Joining computer to domain: $Domain"
        
        if ($OU) {
            Write-Host "Using OU: $OU"
            Add-Computer -DomainName $Domain -Credential $credential -OUPath $OU -Force
        } else {
            Add-Computer -DomainName $Domain -Credential $credential -Force
        }
        
        Write-Host "✓ Domain join completed successfully"
        Write-Host "Note: System restart will be required to complete the process"
        
        return $true
        
    } catch {
        Write-Error "Domain join failed: $($_.Exception.Message)"
        return $false
    }
}

# Main execution
try {
    Write-Host "Domain Join Configuration Script"
    Write-Host "================================"
    
    # Install prerequisites
    if (!(Install-DomainJoinPrerequisites)) {
        throw "Failed to install prerequisites"
    }
    
    if ($PrepareOnly) {
        Write-Host "Preparation mode - configuring for future domain join"
        
        # Just install prerequisites and exit
        Write-Host "✓ System prepared for domain join"
        Write-Host "To join domain later, run the domain join script with actual credentials"
        exit 0
    }
    
    if ($JoinNow -and $DomainName) {
        Write-Host "Attempting to join domain now..."
        
        # Get credentials from SSM Parameter Store
        $domainUser = Get-SSMParameter -ParameterName $DomainUserParameter
        $domainPassword = Get-SSMParameter -ParameterName $DomainPasswordParameter -SecureString
        
        if (!$domainUser -or !$domainPassword) {
            throw "Could not retrieve domain credentials from SSM Parameter Store"
        }
        
        # Configure and join
        if (Set-DomainJoinConfiguration -Domain $DomainName -Username $domainUser -Password $domainPassword -OU $OUPath) {
            if (Join-DomainNow -Domain $DomainName -Username $domainUser -Password $domainPassword -OU $OUPath) {
                Write-Host "✓ Domain join process completed successfully"
            } else {
                throw "Domain join failed"
            }
        } else {
            throw "Domain join configuration failed"
        }
    } else {
        Write-Host "Configuration mode - preparing system for domain join"
        Write-Host "Use -JoinNow switch with -DomainName to perform actual join"
    }
    
    Write-Host "Domain join configuration completed successfully"
    
} catch {
    Write-Error "Domain join configuration failed: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    exit 1
}
