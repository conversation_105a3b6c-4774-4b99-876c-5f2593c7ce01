# AWS Image Builder Quick Start Guide

## Prerequisites Checklist

- [ ] AWS CLI installed and configured
- [ ] PowerShell 5.1 or later
- [ ] VPC with private subnet and NAT Gateway
- [ ] Security group allowing outbound HTTPS, HTTP, DNS, NTP
- [ ] EC2 Key Pair (optional, for troubleshooting)
- [ ] S3 bucket for logs (optional)

## Quick Deployment

### 1. Basic Deployment (Minimal Setup)

```powershell
# Navigate to the ImageBuilder directory
cd C:\Coding\AWS\ImageBuilder

# Run deployment with minimal parameters
.\deployment\deploy.ps1 -Region "us-east-1" -SecurityGroupId "sg-your-sg-id" -SubnetId "subnet-your-subnet-id" -CreateIAMRole -StartBuild
```

### 2. Full Deployment (All Options)

```powershell
# Full deployment with all options
.\deployment\deploy.ps1 `
    -Region "us-east-1" `
    -SecurityGroupId "sg-your-sg-id" `
    -SubnetId "subnet-your-subnet-id" `
    -KeyPairName "your-key-pair" `
    -S3LogsBucket "your-logs-bucket" `
    -TargetAccountIds @("************", "************") `
    -CreateIAMRole `
    -StartBuild
```

## Manual Step-by-Step

### 1. Find Latest Windows Server 2022 AMI

```powershell
aws ec2 describe-images --owners amazon --filters "Name=name,Values=Windows_Server-2022-English-Full-Base-*" --query 'Images | sort_by(@, &CreationDate) | [-1].[ImageId,Name]' --output table
```

### 2. Create IAM Role (if needed)

```powershell
# Create role
aws iam create-role --role-name EC2ImageBuilderInstanceRole --assume-role-policy-document file://trust-policy.json

# Attach policies
aws iam attach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn arn:aws:iam::aws:policy/EC2InstanceProfileForImageBuilder
aws iam attach-role-policy --role-name EC2ImageBuilderInstanceRole --policy-arn arn:aws:iam::aws:policy/SSMInstanceCore

# Create instance profile
aws iam create-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile
aws iam add-role-to-instance-profile --instance-profile-name EC2ImageBuilderInstanceProfile --role-name EC2ImageBuilderInstanceRole
```

### 3. Update Configuration Files

Edit these files with your specific values:
- `infrastructure/build-infrastructure.yml` - Security group, subnet, key pair
- `distribution/distribution-config.yml` - Target accounts and regions
- `recipes/windows-server-2022-custom.yml` - Latest AMI ID

### 4. Deploy Components

```powershell
# Create components
aws imagebuilder create-component --cli-input-yaml file://components/install-dotnet48.yml
aws imagebuilder create-component --cli-input-yaml file://components/install-bginfo.yml
aws imagebuilder create-component --cli-input-yaml file://components/domain-join.yml
```

### 5. Create Configurations

```powershell
# Infrastructure
aws imagebuilder create-infrastructure-configuration --cli-input-yaml file://infrastructure/build-infrastructure.yml

# Distribution
aws imagebuilder create-distribution-configuration --cli-input-yaml file://distribution/distribution-config.yml

# Recipe
aws imagebuilder create-image-recipe --cli-input-yaml file://recipes/windows-server-2022-custom.yml

# Pipeline
aws imagebuilder create-image-pipeline --cli-input-yaml file://pipelines/windows-server-pipeline.yml
```

### 6. Start Build

```powershell
# Get pipeline ARN
$pipelineArn = aws imagebuilder list-image-pipelines --query 'imagePipelineList[?name==`WindowsServer2022CustomPipeline`].arn' --output text

# Start execution
aws imagebuilder start-image-pipeline-execution --image-pipeline-arn $pipelineArn
```

## Monitoring Build Progress

### Check Build Status

```powershell
# List executions
aws imagebuilder list-image-pipeline-executions --image-pipeline-arn "your-pipeline-arn"

# Get detailed status
aws imagebuilder get-image --image-build-version-arn "your-image-build-arn"
```

### View Logs

1. **CloudWatch Logs**: Check `/aws/imagebuilder/` log groups
2. **S3 Bucket**: Build artifacts and detailed logs (if configured)
3. **EC2 Console**: Monitor build instance during execution

## Post-Build Usage

### Launch Instance from Custom AMI

```powershell
# Find your custom AMI
aws ec2 describe-images --owners self --filters "Name=name,Values=CustomWindowsServer2022-*" --query 'Images[*].[ImageId,Name,CreationDate]' --output table

# Launch instance
aws ec2 run-instances --image-id ami-your-custom-ami --instance-type t3.medium --key-name your-key --security-group-ids sg-your-sg --subnet-id subnet-your-subnet
```

### Join Domain (After Launch)

```powershell
# On the launched instance
C:\Windows\Scripts\Join-Domain.ps1 -DomainName "contoso.com" -Username "contoso\administrator" -Password "YourPassword"
```

## Troubleshooting

### Common Issues

1. **Build Fails**: Check CloudWatch logs for specific errors
2. **Permission Denied**: Verify IAM role has required permissions
3. **Network Issues**: Ensure subnet has internet access via NAT Gateway
4. **Component Errors**: Test PowerShell scripts manually on test instance

### Debug Build Instance

```powershell
# Keep instance on failure (update infrastructure config)
# terminateInstanceOnFailure: false

# Connect via Session Manager or RDP
# Check logs in C:\ProgramData\Amazon\ImageBuilder\Logs\
```

## Cleanup

### Quick Cleanup

```powershell
# Remove all resources (except AMIs and IAM role)
.\deployment\cleanup.ps1 -Region "us-east-1"

# Remove everything including AMIs and IAM role
.\deployment\cleanup.ps1 -Region "us-east-1" -DeleteAMIs -DeleteIAMRole -Force
```

### Manual Cleanup

```powershell
# Delete pipeline
aws imagebuilder delete-image-pipeline --image-pipeline-arn "your-pipeline-arn"

# Delete recipe
aws imagebuilder delete-image-recipe --image-recipe-arn "your-recipe-arn"

# Delete configurations
aws imagebuilder delete-distribution-configuration --distribution-configuration-arn "your-dist-config-arn"
aws imagebuilder delete-infrastructure-configuration --infrastructure-configuration-arn "your-infra-config-arn"

# Delete components
aws imagebuilder delete-component --component-build-version-arn "your-component-arn"
```

## Cost Optimization Tips

1. **Instance Type**: Use m5.large for most builds (sufficient for Windows)
2. **Build Schedule**: Use weekly builds to include latest patches
3. **AMI Cleanup**: Regularly delete old AMIs to reduce storage costs
4. **Spot Instances**: Consider for non-production builds (not recommended for production)
5. **Regional Distribution**: Only distribute to regions where needed

## Security Best Practices

1. **Private Subnets**: Always use private subnets with NAT Gateway
2. **Least Privilege**: Use minimal IAM permissions
3. **Encryption**: Enable EBS encryption for build instances
4. **Network Security**: Restrict security group rules to minimum required
5. **Credential Management**: Use SSM Parameter Store for domain credentials
6. **Audit Logging**: Enable CloudTrail for API auditing

## Support Resources

- **AWS Documentation**: https://docs.aws.amazon.com/imagebuilder/
- **PowerShell Help**: `Get-Help .\deployment\deploy.ps1 -Full`
- **AWS CLI Reference**: https://docs.aws.amazon.com/cli/latest/reference/imagebuilder/
- **Troubleshooting Guide**: See README.md for detailed troubleshooting steps
