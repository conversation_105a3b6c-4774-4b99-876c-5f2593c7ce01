# PowerShell Script: Install and Configure BGInfo
# This script downloads, installs, and configures BGInfo to run at startup

param(
    [string]$DownloadUrl = "https://download.sysinternals.com/files/BGInfo.zip",
    [string]$InstallPath = "C:\Program Files\BGInfo",
    [string]$ConfigPath = "C:\Program Files\BGInfo\bginfo.bgi"
)

Write-Host "Starting BGInfo installation and configuration..."

try {
    # Create installation directory
    if (!(Test-Path $InstallPath)) {
        Write-Host "Creating installation directory: $InstallPath"
        New-Item -ItemType Directory -Path $InstallPath -Force | Out-Null
    }

    # Create temp directory for download
    $tempDir = "C:\temp"
    if (!(Test-Path $tempDir)) {
        New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
    }

    # Download BGInfo
    $zipPath = "$tempDir\BGInfo.zip"
    Write-Host "Downloading BGInfo from: $DownloadUrl"
    Invoke-WebRequest -Uri $DownloadUrl -OutFile $zipPath -UseBasicParsing

    # Extract BGInfo
    Write-Host "Extracting BGInfo to: $InstallPath"
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory($zipPath, $InstallPath)

    # Verify BGInfo.exe exists
    $bginfoExe = "$InstallPath\Bginfo.exe"
    if (!(Test-Path $bginfoExe)) {
        throw "BGInfo.exe not found after extraction"
    }

    Write-Host "BGInfo extracted successfully"

    # Create custom BGInfo configuration
    Write-Host "Creating custom BGInfo configuration..."
    
    # Create a basic BGInfo configuration that shows useful system information
    $bgiConfig = @"
[BGInfo]
RTF={\rtf1\ansi\deff0{\fonttbl{\f0\fnil\fcharset0 Arial;}}{\colortbl ;\red255\green255\blue255;\red0\green0\blue0;}
\cf2\fs18 Computer Name: <Computer Name>
IP Address: <IP Address>
Subnet Mask: <Subnet Mask>
Default Gateway: <Default Gateway>
DHCP Server: <DHCP Server>
DNS Server: <DNS Server>
Domain: <Domain>
Logon Domain: <Logon Domain>
Logon Server: <Logon Server>
OS Version: <OS Version>
Service Pack: <Service Pack>
System Type: <System Type>
Processor: <Processor>
Memory: <Memory>
Boot Time: <Boot Time>
Logon Time: <Logon Time>
Free Space C:: <Free Space C:>
Free Space D:: <Free Space D:>
IE Version: <IE Version>
}
Position=0
TextWidth=350
TextHeight=0
Transparency=255
Timeout=0
"@

    # Save configuration to file
    $bgiConfig | Out-File -FilePath $ConfigPath -Encoding ASCII

    # Accept EULA by creating registry entry
    Write-Host "Accepting BGInfo EULA..."
    $registryPath = "HKCU:\Software\Sysinternals\BGInfo"
    if (!(Test-Path $registryPath)) {
        New-Item -Path $registryPath -Force | Out-Null
    }
    Set-ItemProperty -Path $registryPath -Name "EulaAccepted" -Value 1

    # Create startup registry entry for all users
    Write-Host "Configuring BGInfo to run at startup for all users..."
    $startupRegPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
    $startupCommand = "`"$bginfoExe`" `"$ConfigPath`" /timer:0 /nolicprompt"
    Set-ItemProperty -Path $startupRegPath -Name "BGInfo" -Value $startupCommand

    # Run BGInfo immediately to test
    Write-Host "Running BGInfo for the first time..."
    Start-Process -FilePath $bginfoExe -ArgumentList "`"$ConfigPath`"", "/timer:0", "/nolicprompt" -Wait

    # Cleanup
    Write-Host "Cleaning up temporary files..."
    if (Test-Path $zipPath) {
        Remove-Item $zipPath -Force
    }

    Write-Host "BGInfo installation and configuration completed successfully!"
    Write-Host "BGInfo will automatically run at startup and display system information on the desktop."

} catch {
    Write-Error "Failed to install BGInfo: $($_.Exception.Message)"
    Write-Error "Stack Trace: $($_.Exception.StackTrace)"
    exit 1
}
