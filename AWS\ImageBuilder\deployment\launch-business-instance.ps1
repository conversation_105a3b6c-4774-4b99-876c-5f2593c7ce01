# Launch Business-Specific EC2 Instance with Automatic Domain Join
# This script launches an EC2 instance with the correct business configuration and domain join

param(
    [Parameter(Mandatory=$true)]
    [string]$BusinessConfigPath,
    
    [Parameter(Mandatory=$true)]
    [string]$ServerRole,
    
    [Parameter(Mandatory=$true)]
    [string]$AMIId,
    
    [Parameter(Mandatory=$true)]
    [string]$InstanceType = "t3.medium",
    
    [Parameter(Mandatory=$true)]
    [string]$KeyName,
    
    [Parameter(Mandatory=$true)]
    [string]$SecurityGroupId,
    
    [Parameter(Mandatory=$true)]
    [string]$SubnetId,
    
    [Parameter(Mandatory=$false)]
    [string]$DomainUser,
    
    [Parameter(Mandatory=$false)]
    [string]$DomainPassword,
    
    [Parameter(Mandatory=$false)]
    [switch]$UseParameterStore = $false,
    
    [Parameter(Mandatory=$false)]
    [string]$ParameterPrefix = "/ec2/domain",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "us-east-1"
)

Write-Host "Launching Business-Specific EC2 Instance" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Set AWS region
$env:AWS_DEFAULT_REGION = $Region

# Function to check AWS CLI
function Test-AWSConfiguration {
    try {
        $identity = aws sts get-caller-identity --output json | ConvertFrom-Json
        Write-Host "✓ AWS CLI configured for account: $($identity.Account)" -ForegroundColor Green
        return $true
    } catch {
        Write-Error "AWS CLI not configured or not available"
        return $false
    }
}

# Function to validate business configuration
function Test-BusinessConfig {
    param([string]$ConfigPath, [string]$Role)
    
    try {
        if (!(Test-Path $ConfigPath)) {
            throw "Business configuration file not found: $ConfigPath"
        }
        
        $config = Get-Content $ConfigPath -Raw | ConvertFrom-Json
        
        if (!$config.serverOUs.$Role) {
            throw "Server role '$Role' not found in business configuration"
        }
        
        Write-Host "✓ Business configuration validated" -ForegroundColor Green
        Write-Host "  Business: $($config.businessName)" -ForegroundColor Cyan
        Write-Host "  Domain: $($config.domain)" -ForegroundColor Cyan
        Write-Host "  Role: $Role" -ForegroundColor Cyan
        Write-Host "  Target OU: $($config.serverOUs.$Role)" -ForegroundColor Cyan
        
        return $config
        
    } catch {
        Write-Error "Business configuration validation failed: $($_.Exception.Message)"
        return $null
    }
}

# Function to store domain credentials in Parameter Store
function Set-DomainCredentials {
    param([string]$Prefix, [string]$User, [string]$Password)
    
    try {
        Write-Host "Storing domain credentials in Parameter Store..." -ForegroundColor Yellow
        
        # Store username
        aws ssm put-parameter --name "$Prefix/username" --value $User --type "String" --overwrite
        
        # Store password (encrypted)
        aws ssm put-parameter --name "$Prefix/password" --value $Password --type "SecureString" --overwrite
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Domain credentials stored in Parameter Store" -ForegroundColor Green
            return $true
        } else {
            throw "Failed to store credentials in Parameter Store"
        }
        
    } catch {
        Write-Error "Failed to store domain credentials: $($_.Exception.Message)"
        return $false
    }
}

# Function to generate user data
function New-InstanceUserData {
    param([object]$Config, [string]$Role, [string]$User, [string]$Password, [bool]$UseSSM, [string]$Prefix)
    
    try {
        $tempUserDataPath = [System.IO.Path]::GetTempFileName() + ".ps1"
        
        # Use the generate-user-data script
        $generateScript = Join-Path $PSScriptRoot "generate-user-data.ps1"
        
        if (!(Test-Path $generateScript)) {
            throw "Generate user data script not found: $generateScript"
        }
        
        $params = @{
            BusinessConfigPath = $BusinessConfigPath
            ServerRole = $Role
            DomainUser = $User
            DomainPassword = $Password
            OutputPath = $tempUserDataPath
            UseParameterStore = $UseSSM
            ParameterPrefix = $Prefix
        }
        
        & $generateScript @params
        
        if ($LASTEXITCODE -eq 0 -and (Test-Path $tempUserDataPath)) {
            $userData = Get-Content $tempUserDataPath -Raw
            Remove-Item $tempUserDataPath -Force
            return $userData
        } else {
            throw "Failed to generate user data"
        }
        
    } catch {
        Write-Error "Failed to generate user data: $($_.Exception.Message)"
        return $null
    }
}

# Function to launch EC2 instance
function New-EC2Instance {
    param(
        [string]$AMI,
        [string]$Type,
        [string]$Key,
        [string]$SecurityGroup,
        [string]$Subnet,
        [string]$UserData,
        [object]$Config,
        [string]$Role
    )
    
    try {
        Write-Host "Launching EC2 instance..." -ForegroundColor Yellow
        
        # Encode user data
        $userDataBytes = [System.Text.Encoding]::UTF8.GetBytes($UserData)
        $userDataBase64 = [System.Convert]::ToBase64String($userDataBytes)
        
        # Create instance tags
        $tags = @(
            @{ Key = "Name"; Value = "$($Config.businessName)-$Role-$(Get-Date -Format 'yyyyMMdd-HHmm')" },
            @{ Key = "Business"; Value = $Config.businessName },
            @{ Key = "ServerRole"; Value = $Role },
            @{ Key = "Domain"; Value = $Config.domain },
            @{ Key = "AutoDomainJoin"; Value = "true" },
            @{ Key = "CreatedBy"; Value = "ImageBuilder-Automation" }
        )
        
        $tagSpec = @{
            ResourceType = "instance"
            Tags = $tags
        }
        
        # Launch instance
        $launchResult = aws ec2 run-instances `
            --image-id $AMI `
            --count 1 `
            --instance-type $Type `
            --key-name $Key `
            --security-group-ids $SecurityGroup `
            --subnet-id $Subnet `
            --user-data $userDataBase64 `
            --tag-specifications (ConvertTo-Json @($tagSpec) -Depth 10 -Compress) `
            --output json
        
        if ($LASTEXITCODE -eq 0) {
            $instance = ($launchResult | ConvertFrom-Json).Instances[0]
            Write-Host "✓ EC2 instance launched successfully" -ForegroundColor Green
            Write-Host "  Instance ID: $($instance.InstanceId)" -ForegroundColor Cyan
            Write-Host "  Instance Type: $($instance.InstanceType)" -ForegroundColor Cyan
            Write-Host "  State: $($instance.State.Name)" -ForegroundColor Cyan
            
            return $instance
        } else {
            throw "Failed to launch EC2 instance"
        }
        
    } catch {
        Write-Error "Failed to launch EC2 instance: $($_.Exception.Message)"
        return $null
    }
}

# Main execution
try {
    # Validate prerequisites
    if (!(Test-AWSConfiguration)) {
        exit 1
    }
    
    # Validate business configuration
    $businessConfig = Test-BusinessConfig -ConfigPath $BusinessConfigPath -Role $ServerRole
    if (!$businessConfig) {
        exit 1
    }
    
    # Handle domain credentials
    if ($UseParameterStore) {
        if (!$DomainUser -or !$DomainPassword) {
            Write-Error "Domain credentials required when using Parameter Store"
            exit 1
        }
        
        if (!(Set-DomainCredentials -Prefix $ParameterPrefix -User $DomainUser -Password $DomainPassword)) {
            exit 1
        }
    } else {
        if (!$DomainUser -or !$DomainPassword) {
            Write-Error "Domain credentials required"
            exit 1
        }
    }
    
    # Generate user data
    Write-Host "Generating user data for instance..." -ForegroundColor Yellow
    $userData = New-InstanceUserData -Config $businessConfig -Role $ServerRole -User $DomainUser -Password $DomainPassword -UseSSM $UseParameterStore -Prefix $ParameterPrefix
    if (!$userData) {
        exit 1
    }
    
    # Launch instance
    $instance = New-EC2Instance -AMI $AMIId -Type $InstanceType -Key $KeyName -SecurityGroup $SecurityGroupId -Subnet $SubnetId -UserData $userData -Config $businessConfig -Role $ServerRole
    if (!$instance) {
        exit 1
    }
    
    Write-Host "`n✓ Instance launch completed successfully!" -ForegroundColor Green
    Write-Host "`nInstance Details:" -ForegroundColor Yellow
    Write-Host "  Instance ID: $($instance.InstanceId)" -ForegroundColor White
    Write-Host "  Business: $($businessConfig.businessName)" -ForegroundColor White
    Write-Host "  Server Role: $ServerRole" -ForegroundColor White
    Write-Host "  Target Domain: $($businessConfig.domain)" -ForegroundColor White
    Write-Host "  Target OU: $($businessConfig.serverOUs.$ServerRole)" -ForegroundColor White
    
    Write-Host "`nNext Steps:" -ForegroundColor Yellow
    Write-Host "1. Wait for instance to reach 'running' state" -ForegroundColor White
    Write-Host "2. Monitor user data execution logs via EC2 console" -ForegroundColor White
    Write-Host "3. Instance will automatically join domain and restart" -ForegroundColor White
    Write-Host "4. Verify domain join in Active Directory" -ForegroundColor White
    
    Write-Host "`nMonitoring Commands:" -ForegroundColor Yellow
    Write-Host "aws ec2 describe-instances --instance-ids $($instance.InstanceId)" -ForegroundColor Cyan
    Write-Host "aws ec2 get-console-output --instance-id $($instance.InstanceId)" -ForegroundColor Cyan
    
} catch {
    Write-Error "Instance launch failed: $($_.Exception.Message)"
    exit 1
}
